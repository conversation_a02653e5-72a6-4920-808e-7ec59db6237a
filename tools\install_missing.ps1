# Script to install missing dependencies
# This script installs the specific packages that were missing

Write-Host "Installing missing dependencies..." -ForegroundColor Green

# Activate virtual environment if it exists
if (Test-Path ".\venv\Scripts\Activate.ps1") {
    Write-Host "Activating virtual environment..."
    .\venv\Scripts\Activate.ps1
} else {
    Write-Host "Warning: Virtual environment not found. Installing globally." -ForegroundColor Yellow
}

# Install the specific missing package
Write-Host "Installing PyYAML..."
pip install pyyaml==6.0.1

# Install other potentially missing packages
Write-Host "Installing other potentially missing packages..."
pip install chardet==5.2.0 python-dateutil==2.8.2 pytz==2024.1

Write-Host "Installation complete!" -ForegroundColor Green
Write-Host "You can now try running the application again with: .\run.ps1" -ForegroundColor Green
