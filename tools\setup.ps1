# Setup script for PDF Extractor
# This script creates necessary directories and sets up the environment

Write-Host "Setting up PDF Extractor..." -ForegroundColor Green

# Create data directory if it doesn't exist
if (-not (Test-Path ".\data")) {
    Write-Host "Creating data directory..."
    New-Item -ItemType Directory -Path ".\data" -Force
}

# Create other necessary directories
Write-Host "Creating necessary subdirectories..."
New-Item -ItemType Directory -Path ".\logs" -Force
New-Item -ItemType Directory -Path ".\temp" -Force
New-Item -ItemType Directory -Path ".\exported_data" -Force

# Check if Python is installed
try {
    $pythonVersion = python --version
    Write-Host "Found $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "Error: Python not found. Please install Python 3.10 or higher." -ForegroundColor Red
    exit 1
}

# Create virtual environment if it doesn't exist
if (-not (Test-Path ".\venv")) {
    Write-Host "Creating virtual environment..."
    python -m venv venv
}

# Activate virtual environment and install dependencies
Write-Host "Activating virtual environment..."
.\venv\Scripts\Activate

# Upgrade pip
Write-Host "Upgrading pip..."
python -m pip install --upgrade pip

# Install dependencies
Write-Host "Installing dependencies..."
pip install -r requirements.txt

# Check for Java (required by tabula-py)
try {
    $javaVersion = java -version 2>&1
    Write-Host "Found Java for tabula-py" -ForegroundColor Green
} catch {
    Write-Host "Warning: Java not found. Some PDF table extraction features may not work." -ForegroundColor Yellow
    Write-Host "Please install Java Runtime Environment (JRE) for full functionality." -ForegroundColor Yellow
}

Write-Host "Setup complete!" -ForegroundColor Green
Write-Host "You can now run the application with: .\run.ps1" -ForegroundColor Green
