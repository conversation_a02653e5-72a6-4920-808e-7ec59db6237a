# PDF Harvest Hardware ID Utility

Write-Host "===================================================" -ForegroundColor Cyan
Write-Host "PDF Harvest Hardware ID Utility" -ForegroundColor Cyan
Write-Host "===================================================" -ForegroundColor Cyan
Write-Host
Write-Host "This utility will display the hardware ID of this computer" -ForegroundColor Yellow
Write-Host "for use with hardware-locked licenses." -ForegroundColor Yellow
Write-Host

# Check if Python is available
try {
    $pythonVersion = python --version
    Write-Host "Found Python: $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "Error: Python not found. Please install Python 3.8 or higher." -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit
}

# Check if license_manager.py exists
if (-not (Test-Path "license_manager.py")) {
    Write-Host "Error: license_manager.py not found." -ForegroundColor Red
    Write-Host "Please make sure you're running this script from the main application directory." -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit
}

Write-Host "Getting hardware ID..." -ForegroundColor Green
Write-Host

$hardwareId = python -c "from license_manager import get_license_manager; lm = get_license_manager(); print(lm.hardware_id)"

Write-Host "Hardware ID: $hardwareId" -ForegroundColor White
Write-Host
Write-Host "You can use this hardware ID when generating a hardware-locked license." -ForegroundColor Yellow
Write-Host

Read-Host "Press Enter to exit"
