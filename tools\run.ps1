# Run script for PDF Extractor
# This script activates the virtual environment and runs the application

Write-Host "Starting PDF Extractor..." -ForegroundColor Green

# Check if virtual environment exists
if (-not (Test-Path ".\venv")) {
    Write-Host "Virtual environment not found. Running setup script..." -ForegroundColor Yellow
    .\setup.ps1
}

# Activate virtual environment
Write-Host "Activating virtual environment..."
try {
    .\venv\Scripts\Activate
} catch {
    Write-Host "Error activating virtual environment. Please run setup.ps1 first." -ForegroundColor Red
    exit 1
}

# Check for missing dependencies
Write-Host "Checking dependencies..."
$result = python check_dependencies.py
if ($LASTEXITCODE -ne 0) {
    Write-Host "Some dependencies are missing. Running install_missing.ps1..." -ForegroundColor Yellow
    .\install_missing.ps1
}

# Run the application
Write-Host "Starting PDF Extractor application..." -ForegroundColor Green
try {
    python main.py
} catch {
    Write-Host "Error running the application: $_" -ForegroundColor Red
    Write-Host "Please check the logs for more information." -ForegroundColor Red
    exit 1
}
