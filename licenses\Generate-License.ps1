# PDF Harvest License Generator PowerShell Script

function Show-Menu {
    Clear-Host
    Write-Host "===================================================" -ForegroundColor Cyan
    Write-Host "PDF Harvest License Generator" -ForegroundColor Cyan
    Write-Host "===================================================" -ForegroundColor Cyan
    Write-Host
    Write-Host "Please select an option:" -ForegroundColor Yellow
    Write-Host
    Write-Host "1. Generate Demo License" -ForegroundColor White
    Write-Host "2. Generate Basic License" -ForegroundColor White
    Write-Host "3. Generate Professional License" -ForegroundColor White
    Write-Host "4. Generate Custom License" -ForegroundColor White
    Write-Host "5. List Available License Editions" -ForegroundColor White
    Write-Host "6. Decode Existing License Key" -ForegroundColor White
    Write-Host "7. Exit" -ForegroundColor White
    Write-Host
}

function Generate-DemoLicense {
    Clear-Host
    Write-Host "===================================================" -ForegroundColor Cyan
    Write-Host "Generate Demo License" -ForegroundColor Cyan
    Write-Host "===================================================" -ForegroundColor Cyan
    Write-Host
    Write-Host "Please enter the parameters for your demo license:" -ForegroundColor Yellow
    Write-Host
    
    $days = Read-Host "Expiry days (default: 30)"
    $fileLimit = Read-Host "File limit (default: 10)"
    
    if ([string]::IsNullOrEmpty($days)) { $days = 30 }
    if ([string]::IsNullOrEmpty($fileLimit)) { $fileLimit = 10 }
    
    Write-Host
    Write-Host "Generating demo license with:" -ForegroundColor Green
    Write-Host "- Expiry days: $days" -ForegroundColor White
    Write-Host "- File limit: $fileLimit" -ForegroundColor White
    Write-Host
    
    python tools\license_generator.py demo --days $days --file-limit $fileLimit
    
    Write-Host
    $saveFile = Read-Host "Save license key to file? (Y/N)"
    if ($saveFile -eq "Y" -or $saveFile -eq "y") {
        $filename = Read-Host "Enter filename (default: demo_license.txt)"
        if ([string]::IsNullOrEmpty($filename)) { $filename = "demo_license.txt" }
        
        python tools\license_generator.py demo --days $days --file-limit $fileLimit --output $filename
        Write-Host "License key saved to $filename" -ForegroundColor Green
    }
    
    Write-Host
    Read-Host "Press Enter to continue"
}

function Generate-BasicLicense {
    Clear-Host
    Write-Host "===================================================" -ForegroundColor Cyan
    Write-Host "Generate Basic License" -ForegroundColor Cyan
    Write-Host "===================================================" -ForegroundColor Cyan
    Write-Host
    Write-Host "Please enter the parameters for your basic license:" -ForegroundColor Yellow
    Write-Host
    
    $days = Read-Host "Expiry days (default: 365)"
    $fileLimit = Read-Host "File limit (default: 50)"
    $hardwareId = Read-Host "Hardware ID (leave empty for no hardware lock)"
    
    if ([string]::IsNullOrEmpty($days)) { $days = 365 }
    if ([string]::IsNullOrEmpty($fileLimit)) { $fileLimit = 50 }
    
    Write-Host
    Write-Host "Generating basic license with:" -ForegroundColor Green
    Write-Host "- Edition: Basic" -ForegroundColor White
    Write-Host "- Expiry days: $days" -ForegroundColor White
    Write-Host "- File limit: $fileLimit" -ForegroundColor White
    if (-not [string]::IsNullOrEmpty($hardwareId)) {
        Write-Host "- Hardware ID: $hardwareId" -ForegroundColor White
    }
    Write-Host
    
    $cmd = "python tools\license_generator.py generate --edition basic --expiry-days $days --file-limit $fileLimit"
    if (-not [string]::IsNullOrEmpty($hardwareId)) {
        $cmd += " --hardware-id `"$hardwareId`""
    }
    
    Invoke-Expression $cmd
    
    Write-Host
    $saveFile = Read-Host "Save license key to file? (Y/N)"
    if ($saveFile -eq "Y" -or $saveFile -eq "y") {
        $filename = Read-Host "Enter filename (default: basic_license.txt)"
        if ([string]::IsNullOrEmpty($filename)) { $filename = "basic_license.txt" }
        
        $saveCmd = "$cmd --output $filename"
        Invoke-Expression $saveCmd
        
        Write-Host "License key saved to $filename" -ForegroundColor Green
    }
    
    Write-Host
    Read-Host "Press Enter to continue"
}

function Generate-ProfessionalLicense {
    Clear-Host
    Write-Host "===================================================" -ForegroundColor Cyan
    Write-Host "Generate Professional License" -ForegroundColor Cyan
    Write-Host "===================================================" -ForegroundColor Cyan
    Write-Host
    Write-Host "Please enter the parameters for your professional license:" -ForegroundColor Yellow
    Write-Host
    
    $days = Read-Host "Expiry days (default: 365)"
    $fileLimit = Read-Host "File limit (default: 500)"
    $hardwareId = Read-Host "Hardware ID (leave empty for no hardware lock)"
    
    if ([string]::IsNullOrEmpty($days)) { $days = 365 }
    if ([string]::IsNullOrEmpty($fileLimit)) { $fileLimit = 500 }
    
    Write-Host
    Write-Host "Generating professional license with:" -ForegroundColor Green
    Write-Host "- Edition: Professional" -ForegroundColor White
    Write-Host "- Expiry days: $days" -ForegroundColor White
    Write-Host "- File limit: $fileLimit" -ForegroundColor White
    if (-not [string]::IsNullOrEmpty($hardwareId)) {
        Write-Host "- Hardware ID: $hardwareId" -ForegroundColor White
    }
    Write-Host
    
    $cmd = "python tools\license_generator.py generate --edition professional --expiry-days $days --file-limit $fileLimit"
    if (-not [string]::IsNullOrEmpty($hardwareId)) {
        $cmd += " --hardware-id `"$hardwareId`""
    }
    
    Invoke-Expression $cmd
    
    Write-Host
    $saveFile = Read-Host "Save license key to file? (Y/N)"
    if ($saveFile -eq "Y" -or $saveFile -eq "y") {
        $filename = Read-Host "Enter filename (default: professional_license.txt)"
        if ([string]::IsNullOrEmpty($filename)) { $filename = "professional_license.txt" }
        
        $saveCmd = "$cmd --output $filename"
        Invoke-Expression $saveCmd
        
        Write-Host "License key saved to $filename" -ForegroundColor Green
    }
    
    Write-Host
    Read-Host "Press Enter to continue"
}

function Generate-CustomLicense {
    Clear-Host
    Write-Host "===================================================" -ForegroundColor Cyan
    Write-Host "Generate Custom License" -ForegroundColor Cyan
    Write-Host "===================================================" -ForegroundColor Cyan
    Write-Host
    Write-Host "Please enter the parameters for your custom license:" -ForegroundColor Yellow
    Write-Host
    
    $edition = Read-Host "Edition (demo/basic/professional)"
    $days = Read-Host "Expiry days"
    $fileLimit = Read-Host "File limit"
    $hardwareId = Read-Host "Hardware ID (leave empty for no hardware lock)"
    $features = Read-Host "Features (space-separated, e.g., basic_extraction export_data)"
    
    if ([string]::IsNullOrEmpty($edition)) {
        Write-Host "Edition is required." -ForegroundColor Red
        Read-Host "Press Enter to try again"
        Generate-CustomLicense
        return
    }
    
    Write-Host
    Write-Host "Generating custom license with:" -ForegroundColor Green
    Write-Host "- Edition: $edition" -ForegroundColor White
    if (-not [string]::IsNullOrEmpty($days)) {
        Write-Host "- Expiry days: $days" -ForegroundColor White
    }
    if (-not [string]::IsNullOrEmpty($fileLimit)) {
        Write-Host "- File limit: $fileLimit" -ForegroundColor White
    }
    if (-not [string]::IsNullOrEmpty($hardwareId)) {
        Write-Host "- Hardware ID: $hardwareId" -ForegroundColor White
    }
    if (-not [string]::IsNullOrEmpty($features)) {
        Write-Host "- Features: $features" -ForegroundColor White
    }
    Write-Host
    
    $cmd = "python tools\license_generator.py generate --edition $edition"
    
    if (-not [string]::IsNullOrEmpty($days)) {
        $cmd += " --expiry-days $days"
    }
    if (-not [string]::IsNullOrEmpty($fileLimit)) {
        $cmd += " --file-limit $fileLimit"
    }
    if (-not [string]::IsNullOrEmpty($hardwareId)) {
        $cmd += " --hardware-id `"$hardwareId`""
    }
    if (-not [string]::IsNullOrEmpty($features)) {
        $cmd += " --features $features"
    }
    
    Invoke-Expression $cmd
    
    Write-Host
    $saveFile = Read-Host "Save license key to file? (Y/N)"
    if ($saveFile -eq "Y" -or $saveFile -eq "y") {
        $filename = Read-Host "Enter filename (default: custom_license.txt)"
        if ([string]::IsNullOrEmpty($filename)) { $filename = "custom_license.txt" }
        
        $saveCmd = "$cmd --output $filename"
        Invoke-Expression $saveCmd
        
        Write-Host "License key saved to $filename" -ForegroundColor Green
    }
    
    Write-Host
    Read-Host "Press Enter to continue"
}

function List-Editions {
    Clear-Host
    Write-Host "===================================================" -ForegroundColor Cyan
    Write-Host "List Available License Editions" -ForegroundColor Cyan
    Write-Host "===================================================" -ForegroundColor Cyan
    Write-Host
    
    python tools\license_generator.py list-editions
    
    Write-Host
    Read-Host "Press Enter to continue"
}

function Decode-License {
    Clear-Host
    Write-Host "===================================================" -ForegroundColor Cyan
    Write-Host "Decode Existing License Key" -ForegroundColor Cyan
    Write-Host "===================================================" -ForegroundColor Cyan
    Write-Host
    Write-Host "Please enter the license key to decode:" -ForegroundColor Yellow
    Write-Host
    
    $licenseKey = Read-Host "License Key"
    
    if ([string]::IsNullOrEmpty($licenseKey)) {
        Write-Host "License key is required." -ForegroundColor Red
        Read-Host "Press Enter to try again"
        Decode-License
        return
    }
    
    Write-Host
    Write-Host "Decoding license key:" -ForegroundColor Green
    Write-Host $licenseKey -ForegroundColor White
    Write-Host
    
    python tools\license_generator.py decode "$licenseKey"
    
    Write-Host
    Read-Host "Press Enter to continue"
}

# Check if Python is available
try {
    $pythonVersion = python --version
    Write-Host "Found Python: $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "Error: Python not found. Please install Python 3.8 or higher." -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit
}

# Check if license_generator.py exists
if (-not (Test-Path "tools\license_generator.py")) {
    Write-Host "Error: license_generator.py not found in the tools directory." -ForegroundColor Red
    Write-Host "Please make sure you're running this script from the main application directory." -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit
}

# Main menu loop
$choice = ""
while ($choice -ne "7") {
    Show-Menu
    $choice = Read-Host "Enter your choice (1-7)"
    
    switch ($choice) {
        "1" { Generate-DemoLicense }
        "2" { Generate-BasicLicense }
        "3" { Generate-ProfessionalLicense }
        "4" { Generate-CustomLicense }
        "5" { List-Editions }
        "6" { Decode-License }
        "7" { 
            Write-Host
            Write-Host "Thank you for using the PDF Harvest License Generator." -ForegroundColor Cyan
            Write-Host
        }
        default {
            Write-Host "Invalid choice. Please try again." -ForegroundColor Red
            Start-Sleep -Seconds 2
        }
    }
}
