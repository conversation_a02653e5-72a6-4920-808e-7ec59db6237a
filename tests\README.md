# PDF Extractor Testing

This directory contains tests for the PDF Extractor project.

## Test Structure

- `unit/`: Unit tests for individual components
- `integration/`: Integration tests for component interactions
- `data/`: Test data files (sample PDFs, etc.)
- `conftest.py`: Pytest configuration and fixtures
- `run_tests.py`: <PERSON>ript to run all tests

## Running Tests

### Prerequisites

Install the required testing packages:

```bash
pip install -r requirements-test.txt
```

### Running All Tests

```bash
python run_tests.py
```

### Running Specific Tests

```bash
# Run unit tests only
pytest unit/

# Run a specific test file
pytest unit/test_pdf_extraction_utils.py

# Run a specific test class
pytest unit/test_pdf_extraction_utils.py::TestCacheManagement

# Run a specific test method
pytest unit/test_pdf_extraction_utils.py::TestCacheManagement::test_cache_key_generation
```

## Test Data

To run tests that require sample PDFs, place your test PDFs in the `data/` directory.
The main sample PDF should be named `sample.pdf`.

## Test Reports

After running tests with `run_tests.py`, an HTML report will be generated as `test_report.html`.

## Adding New Tests

When adding new tests:

1. Follow the existing structure (unit tests in `unit/`, integration tests in `integration/`)
2. Use descriptive test class and method names
3. Add appropriate docstrings
4. Use fixtures from `conftest.py` where appropriate
5. Mock external dependencies when possible
