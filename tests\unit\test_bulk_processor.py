"""
Unit tests for bulk_processor.py
"""
import os
import pytest
import pandas as pd
import sqlite3
from unittest.mock import patch, MagicMock, call

from bulk_processor import (
    BulkProcessor,
    CustomJSONEncoder
)

class TestCustomJSONEncoder:
    """Tests for the CustomJSONEncoder class."""
    
    def test_encode_datetime(self):
        """Test encoding datetime objects."""
        from datetime import datetime
        
        # Create a datetime object
        dt = datetime(2023, 1, 1, 12, 0, 0)
        
        # Create an encoder
        encoder = CustomJSONEncoder()
        
        # Encode the datetime
        result = encoder.default(dt)
        
        # Check the result
        assert result == "2023-01-01T12:00:00", "Datetime should be encoded as ISO format"
    
    def test_encode_decimal(self):
        """Test encoding Decimal objects."""
        from decimal import Decimal
        
        # Create a Decimal object
        dec = Decimal("123.45")
        
        # Create an encoder
        encoder = CustomJSONEncoder()
        
        # Encode the Decimal
        result = encoder.default(dec)
        
        # Check the result
        assert result == 123.45, "Decimal should be encoded as float"
        assert isinstance(result, float), "Result should be a float"
    
    def test_encode_nan(self):
        """Test encoding NaN values."""
        import numpy as np
        
        # Create a NaN value
        nan_value = pd.NA
        
        # Create an encoder
        encoder = CustomJSONEncoder()
        
        # Encode the NaN
        result = encoder.default(nan_value)
        
        # Check the result
        assert result is None, "NaN should be encoded as None"

class TestBulkProcessorInitialization:
    """Tests for BulkProcessor initialization."""
    
    @patch('bulk_processor.get_license_manager')
    def test_init(self, mock_get_license_manager):
        """Test initialization of BulkProcessor."""
        # Mock the license manager
        mock_license_manager = MagicMock()
        mock_get_license_manager.return_value = mock_license_manager
        
        # Create a BulkProcessor
        processor = BulkProcessor()
        
        # Check initialization
        assert processor.pdf_files == [], "PDF files list should be empty"
        assert processor.processed_data == {}, "Processed data dict should be empty"
        assert processor.should_stop is False, "Should stop flag should be False"
        assert processor.memory_threshold == 80, "Memory threshold should be 80%"
        assert processor.batch_size == 10, "Batch size should be 10"
        assert processor.license_manager == mock_license_manager, "License manager should be set"

class TestBulkProcessorLicenseManagement:
    """Tests for BulkProcessor license management."""
    
    @patch('bulk_processor.get_license_manager')
    def test_load_license_info_valid(self, mock_get_license_manager):
        """Test loading valid license information."""
        # Mock the license manager
        mock_license_manager = MagicMock()
        mock_license_manager.verify_license.return_value = (True, "Valid license")
        mock_license_manager.get_license_info.return_value = {
            "file_limit": 100,
            "expiry_date": "2023-12-31T00:00:00"
        }
        mock_license_manager.get_files_processed.return_value = 50
        mock_get_license_manager.return_value = mock_license_manager
        
        # Create a BulkProcessor
        processor = BulkProcessor()
        
        # Mock the UI elements
        processor.license_status_label = MagicMock()
        processor.license_usage_label = MagicMock()
        processor.license_expiry_label = MagicMock()
        
        # Load license info
        processor.load_license_info()
        
        # Check license info
        assert processor.license_valid is True, "License should be valid"
        assert processor.file_limit == 100, "File limit should be 100"
        assert processor.files_processed == 50, "Files processed should be 50"
        
        # Check UI updates
        processor.license_status_label.setText.assert_called_once_with("Valid")
        processor.license_usage_label.setText.assert_called_once_with("50/100")
        processor.license_expiry_label.setText.assert_called_once_with("2023-12-31")
    
    @patch('bulk_processor.get_license_manager')
    def test_load_license_info_invalid(self, mock_get_license_manager):
        """Test loading invalid license information."""
        # Mock the license manager
        mock_license_manager = MagicMock()
        mock_license_manager.verify_license.return_value = (False, "Invalid license")
        mock_license_manager.get_license_info.return_value = {}
        mock_license_manager.get_files_processed.return_value = 0
        mock_get_license_manager.return_value = mock_license_manager
        
        # Create a BulkProcessor
        processor = BulkProcessor()
        
        # Mock the UI elements
        processor.license_status_label = MagicMock()
        processor.license_usage_label = MagicMock()
        processor.license_expiry_label = MagicMock()
        
        # Load license info
        processor.load_license_info()
        
        # Check license info
        assert processor.license_valid is False, "License should be invalid"
        assert processor.file_limit == 0, "File limit should be 0"
        assert processor.files_processed == 0, "Files processed should be 0"
        
        # Check UI updates
        processor.license_status_label.setText.assert_called_once_with("Invalid")
        processor.license_usage_label.setText.assert_called_once_with("0/0")
        processor.license_expiry_label.setText.assert_called_once_with("Unknown")
    
    @patch('bulk_processor.get_license_manager')
    def test_update_license_usage(self, mock_get_license_manager):
        """Test updating license usage."""
        # Mock the license manager
        mock_license_manager = MagicMock()
        mock_license_manager.update_files_processed.return_value = True
        mock_license_manager.get_files_processed.return_value = 60
        mock_get_license_manager.return_value = mock_license_manager
        
        # Create a BulkProcessor
        processor = BulkProcessor()
        processor.file_limit = 100
        processor.files_processed = 50
        
        # Mock the UI elements
        processor.license_usage_label = MagicMock()
        
        # Update license usage
        processor.update_license_usage(10)
        
        # Check license info
        assert processor.files_processed == 60, "Files processed should be updated to 60"
        
        # Check UI updates
        processor.license_usage_label.setText.assert_called_once_with("60/100")
        
        # Check license manager calls
        mock_license_manager.update_files_processed.assert_called_once_with(10)
        mock_license_manager.get_files_processed.assert_called_once()
    
    @patch('bulk_processor.get_license_manager')
    @patch('bulk_processor.QMessageBox')
    def test_check_license_limit_allowed(self, mock_qmessagebox, mock_get_license_manager):
        """Test checking license limit when allowed."""
        # Mock the license manager
        mock_license_manager = MagicMock()
        mock_license_manager.check_bulk_limit.return_value = (True, "")
        mock_get_license_manager.return_value = mock_license_manager
        
        # Create a BulkProcessor
        processor = BulkProcessor()
        
        # Check license limit
        result = processor.check_license_limit(5)
        
        # Check result
        assert result is True, "License check should pass"
        
        # Check license manager calls
        mock_license_manager.check_bulk_limit.assert_called_once_with(5)
        
        # Check that no message box was shown
        mock_qmessagebox.critical.assert_not_called()
    
    @patch('bulk_processor.get_license_manager')
    @patch('bulk_processor.QMessageBox')
    def test_check_license_limit_not_allowed(self, mock_qmessagebox, mock_get_license_manager):
        """Test checking license limit when not allowed."""
        # Mock the license manager
        mock_license_manager = MagicMock()
        mock_license_manager.check_bulk_limit.return_value = (False, "License limit exceeded")
        mock_get_license_manager.return_value = mock_license_manager
        
        # Create a BulkProcessor
        processor = BulkProcessor()
        
        # Check license limit
        result = processor.check_license_limit(50)
        
        # Check result
        assert result is False, "License check should fail"
        
        # Check license manager calls
        mock_license_manager.check_bulk_limit.assert_called_once_with(50)
        
        # Check that a message box was shown
        mock_qmessagebox.critical.assert_called_once()

class TestBulkProcessorTemplateManagement:
    """Tests for BulkProcessor template management."""
    
    @patch('bulk_processor.sqlite3.connect')
    @patch('bulk_processor.QMessageBox')
    def test_load_templates_success(self, mock_qmessagebox, mock_connect):
        """Test loading templates successfully."""
        # Mock the database connection
        mock_conn = MagicMock()
        mock_cursor = MagicMock()
        mock_conn.cursor.return_value = mock_cursor
        mock_connect.return_value = mock_conn
        
        # Mock the cursor results
        mock_cursor.fetchone.return_value = True  # Table exists
        mock_cursor.fetchall.side_effect = [
            # Column info
            [(0, 'id', 'INTEGER', 0, None, 1), 
             (1, 'name', 'TEXT', 0, None, 0),
             (2, 'template_type', 'TEXT', 0, None, 0),
             (3, 'page_count', 'INTEGER', 0, None, 0)],
            # Templates
            [(1, 'Invoice Template', 'single', 1),
             (2, 'Multi-page Template', 'multi', 3)]
        ]
        
        # Create a BulkProcessor
        processor = BulkProcessor()
        
        # Mock the UI elements
        processor.template_combo = MagicMock()
        processor.multi_page_label = MagicMock()
        
        # Load templates
        processor.load_templates()
        
        # Check database calls
        mock_connect.assert_called_once_with("invoice_templates.db")
        mock_cursor.execute.assert_any_call(
            "SELECT name FROM sqlite_master WHERE type='table' AND name='templates'"
        )
        mock_cursor.execute.assert_any_call("PRAGMA table_info(templates)")
        
        # Check UI updates
        assert processor.template_combo.clear.called, "Template combo should be cleared"
        assert processor.template_combo.addItem.call_count == 2, "Two templates should be added"
        processor.multi_page_label.setText.assert_called_once_with("Multi-page support: Enabled ✓")
    
    @patch('bulk_processor.sqlite3.connect')
    @patch('bulk_processor.QMessageBox')
    def test_load_templates_no_table(self, mock_qmessagebox, mock_connect):
        """Test loading templates when the table doesn't exist."""
        # Mock the database connection
        mock_conn = MagicMock()
        mock_cursor = MagicMock()
        mock_conn.cursor.return_value = mock_cursor
        mock_connect.return_value = mock_conn
        
        # Mock the cursor results
        mock_cursor.fetchone.return_value = None  # Table doesn't exist
        
        # Create a BulkProcessor
        processor = BulkProcessor()
        
        # Mock the UI elements
        processor.template_combo = MagicMock()
        processor.multi_page_label = MagicMock()
        
        # Load templates
        processor.load_templates()
        
        # Check database calls
        mock_connect.assert_called_once_with("invoice_templates.db")
        mock_cursor.execute.assert_called_once_with(
            "SELECT name FROM sqlite_master WHERE type='table' AND name='templates'"
        )
        
        # Check UI updates
        mock_qmessagebox.warning.assert_called_once()
        assert not processor.template_combo.addItem.called, "No templates should be added"
