# Build configuration for creating standalone executable
param(
    [switch]$Release = $false
)

Write-Host "Setting up build environment..."

# Create virtual environment if it doesn't exist
if (-not (Test-Path ".\venv")) {
    python -m venv venv
    Write-Host "Created virtual environment"
}

# Activate virtual environment
.\venv\Scripts\Activate
Write-Host "Activated virtual environment"

# Install requirements
pip install -r requirements.txt
Write-Host "Installed dependencies"

# Create dist directory
New-Item -ItemType Directory -Path "dist" -Force
Write-Host "Created dist directory"

# Build with Nuitka
$BuildCommand = "python -m nuitka --follow-imports --enable-plugin=pyside6 --windows-disable-console"

if ($Release) {
    # Add release mode optimizations
    $BuildCommand += " --remove-output --assume-yes-for-downloads --standalone"
}

$BuildCommand += " --output-dir=dist main.py"

Write-Host "Building executable..."
Invoke-Expression $BuildCommand

# Copy necessary files
Copy-Item "requirements.txt" -Destination "dist"
Copy-Item "README.md" -Destination "dist"
Copy-Item "icon.png" -Destination "dist"

# Create data directories
New-Item -ItemType Directory -Path "dist\data" -Force
New-Item -ItemType Directory -Path "dist\logs" -Force
New-Item -ItemType Directory -Path "dist\temp" -Force
New-Item -ItemType Directory -Path "dist\exported_data" -Force

Write-Host "Build complete! Check the 'dist' folder for the executable."
