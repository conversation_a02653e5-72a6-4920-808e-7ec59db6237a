#!/usr/bin/env python3
"""
Minimal test to verify the multi-method extraction system is working
"""

import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    print("Starting minimal test...")
    
    try:
        print("1. Testing extraction_params_utils import...")
        from extraction_params_utils import validate_extraction_method, SUPPORTED_EXTRACTION_METHODS
        print(f"   ✅ Supported methods: {SUPPORTED_EXTRACTION_METHODS}")
        
        print("2. Testing method validation...")
        for method in SUPPORTED_EXTRACTION_METHODS:
            is_valid = validate_extraction_method(method)
            print(f"   ✅ {method}: {is_valid}")
        
        print("3. Testing multi_method_extraction import...")
        from multi_method_extraction import MultiMethodExtractor
        print("   ✅ MultiMethodExtractor imported")
        
        print("4. Creating extractor instance...")
        extractor = MultiMethodExtractor()
        print("   ✅ Extractor created")
        
        print("5. Testing method availability...")
        methods = {
            'pypdf_table_extraction': hasattr(extractor, '_extract_with_pypdf'),
            'pdftotext': hasattr(extractor, '_extract_with_pdftotext'),
            'tesseract_ocr': hasattr(extractor, '_extract_with_tesseract')
        }
        
        for method, available in methods.items():
            print(f"   ✅ {method}: {'available' if available else 'not available'}")
        
        print("6. Testing cleanup...")
        extractor.cleanup()
        print("   ✅ Cleanup completed")
        
        print("\n🎉 All tests passed! Multi-method extraction system is working.")
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    print(f"\nTest result: {'PASSED' if success else 'FAILED'}")
    sys.exit(0 if success else 1)
