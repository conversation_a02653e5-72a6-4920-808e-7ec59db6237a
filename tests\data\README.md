# Test Data

This directory contains test data files for the PDF Extractor project.

## Required Files

For the tests to run successfully, you need to add the following files:

- `sample.pdf`: A basic sample PDF for general testing
- `samples/invoice_sample.pdf`: A sample invoice PDF
- `samples/multi_page_sample.pdf`: A sample multi-page PDF

## Creating Test PDFs

If you don't have suitable test PDFs, you can create them using tools like:

- LibreOffice Writer (save as PDF)
- Microsoft Word (save as PDF)
- Online PDF generators

## PDF Requirements

The test PDFs should have the following characteristics:

### sample.pdf
- Single page
- Contains some text content
- Has at least one table-like structure

### samples/invoice_sample.pdf
- Contains header section with company information
- Contains items section with a table of line items
- Contains summary section with totals

### samples/multi_page_sample.pdf
- At least 3 pages
- Contains tables that span multiple pages
