#!/usr/bin/env python3
"""
Test script for multi-method PDF extraction system

This script tests the integration of pypdf_table_extraction, pdftotext, and tesseract_ocr
extraction methods with the existing invoice processing system.
"""

import os
import sys
import tempfile
import pandas as pd
from typing import Dict, List, Any

# Add the current directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_extraction_method_validation():
    """Test extraction method validation"""
    print("Testing extraction method validation...")
    
    try:
        from extraction_params_utils import validate_extraction_method, SUPPORTED_EXTRACTION_METHODS
        
        # Test valid methods
        for method in SUPPORTED_EXTRACTION_METHODS:
            assert validate_extraction_method(method), f"Valid method {method} failed validation"
            print(f"✓ {method} - valid")
        
        # Test invalid method
        assert not validate_extraction_method("invalid_method"), "Invalid method passed validation"
        print("✓ invalid_method - correctly rejected")
        
        print("✓ Extraction method validation tests passed\n")
        return True
        
    except Exception as e:
        print(f"✗ Extraction method validation tests failed: {e}\n")
        return False

def test_multi_method_extractor():
    """Test the MultiMethodExtractor class"""
    print("Testing MultiMethodExtractor class...")
    
    try:
        from multi_method_extraction import MultiMethodExtractor
        
        # Create extractor instance
        extractor = MultiMethodExtractor()
        print("✓ MultiMethodExtractor instance created")
        
        # Test method availability
        methods_available = {
            'pypdf_table_extraction': True,  # Always available
            'pdftotext': hasattr(extractor, '_extract_with_pdftotext'),
            'tesseract_ocr': hasattr(extractor, '_extract_with_tesseract')
        }
        
        for method, available in methods_available.items():
            status = "available" if available else "not available"
            print(f"✓ {method} - {status}")
        
        # Test cleanup
        extractor.cleanup()
        print("✓ Cleanup completed")
        
        print("✓ MultiMethodExtractor tests passed\n")
        return True
        
    except Exception as e:
        print(f"✗ MultiMethodExtractor tests failed: {e}\n")
        return False

def test_extraction_params_utils():
    """Test extraction parameters utilities"""
    print("Testing extraction parameters utilities...")
    
    try:
        from extraction_params_utils import (
            normalize_extraction_params,
            prepare_extraction_method_params,
            DEFAULT_SECTION_PARAMS
        )
        
        # Test parameter normalization
        raw_params = {
            'header': {'row_tol': 5},
            'items': {'row_tol': 15},
            'summary': {'row_tol': 10},
            'flavor': 'stream'
        }
        
        normalized = normalize_extraction_params(raw_params)
        assert isinstance(normalized, dict), "Normalized params should be a dict"
        print("✓ Parameter normalization works")
        
        # Test method-specific parameter preparation
        for method in ['pypdf_table_extraction', 'pdftotext', 'tesseract_ocr']:
            method_params = prepare_extraction_method_params(method, raw_params)
            assert isinstance(method_params, dict), f"Method params for {method} should be a dict"
            print(f"✓ {method} parameter preparation works")
        
        # Test default section parameters
        assert 'header' in DEFAULT_SECTION_PARAMS, "Default params should include header"
        assert 'items' in DEFAULT_SECTION_PARAMS, "Default params should include items"
        assert 'summary' in DEFAULT_SECTION_PARAMS, "Default params should include summary"
        print("✓ Default section parameters are properly defined")
        
        print("✓ Extraction parameters utilities tests passed\n")
        return True
        
    except Exception as e:
        print(f"✗ Extraction parameters utilities tests failed: {e}\n")
        return False

def test_database_schema():
    """Test database schema for extraction method support"""
    print("Testing database schema for extraction method...")
    
    try:
        import sqlite3
        
        # Check if database exists
        if not os.path.exists("invoice_templates.db"):
            print("⚠ Database file not found, skipping database tests")
            return True
        
        # Connect to database
        conn = sqlite3.connect("invoice_templates.db")
        cursor = conn.cursor()
        
        # Check if extraction_method column exists
        cursor.execute("PRAGMA table_info(templates)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'extraction_method' in columns:
            print("✓ extraction_method column exists in templates table")
            
            # Test inserting a template with extraction method
            test_template = {
                'name': 'test_multi_method',
                'description': 'Test template for multi-method extraction',
                'template_type': 'single',
                'extraction_method': 'pdftotext',
                'regions': '{}',
                'column_lines': '{}',
                'config': '{}'
            }
            
            try:
                cursor.execute("""
                    INSERT INTO templates (name, description, template_type, extraction_method, regions, column_lines, config)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (
                    test_template['name'],
                    test_template['description'],
                    test_template['template_type'],
                    test_template['extraction_method'],
                    test_template['regions'],
                    test_template['column_lines'],
                    test_template['config']
                ))
                
                # Get the inserted template
                cursor.execute("SELECT extraction_method FROM templates WHERE name = ?", (test_template['name'],))
                result = cursor.fetchone()
                
                if result and result[0] == 'pdftotext':
                    print("✓ Successfully inserted and retrieved template with extraction method")
                else:
                    print("✗ Failed to retrieve correct extraction method")
                
                # Clean up test template
                cursor.execute("DELETE FROM templates WHERE name = ?", (test_template['name'],))
                conn.commit()
                
            except Exception as e:
                print(f"✗ Database operation failed: {e}")
        else:
            print("⚠ extraction_method column not found in templates table")
            print("  Run the database migration script to add this column")
        
        conn.close()
        print("✓ Database schema tests completed\n")
        return True
        
    except Exception as e:
        print(f"✗ Database schema tests failed: {e}\n")
        return False

def test_integration():
    """Test integration with existing systems"""
    print("Testing integration with existing systems...")
    
    try:
        # Test import of updated modules
        from bulk_processor import BulkProcessor
        print("✓ BulkProcessor imports successfully")
        
        from split_screen_invoice_processor import SplitScreenInvoiceProcessor
        print("✓ SplitScreenInvoiceProcessor imports successfully")
        
        # Test that multi-method extraction is available
        from multi_method_extraction import extract_with_method
        print("✓ extract_with_method function is available")
        
        # Test that extraction method can be set
        processor = SplitScreenInvoiceProcessor()
        if hasattr(processor, 'current_extraction_method'):
            print("✓ SplitScreenInvoiceProcessor has current_extraction_method attribute")
            
            # Test method change
            processor.current_extraction_method = 'pdftotext'
            assert processor.current_extraction_method == 'pdftotext'
            print("✓ Extraction method can be changed")
        else:
            print("✗ SplitScreenInvoiceProcessor missing current_extraction_method attribute")
        
        print("✓ Integration tests passed\n")
        return True
        
    except Exception as e:
        print(f"✗ Integration tests failed: {e}\n")
        return False

def main():
    """Run all tests"""
    print("=" * 60)
    print("Multi-Method PDF Extraction System Tests")
    print("=" * 60)
    print()
    
    tests = [
        test_extraction_method_validation,
        test_multi_method_extractor,
        test_extraction_params_utils,
        test_database_schema,
        test_integration
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("=" * 60)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Multi-method extraction system is ready.")
    else:
        print("⚠ Some tests failed. Please check the output above.")
    
    print("=" * 60)
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
