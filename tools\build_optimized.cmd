@echo off
setlocal enabledelayedexpansion

echo ===================================================
echo Building Optimized PDFHarvest Executable
echo ===================================================
echo.

REM Check if Python is available
where python >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo Error: Python not found. Please install Python 3.8 or higher.
    pause
    exit /b 1
)

REM Check if Nuitka is installed
python -c "import nuitka" >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo Nuitka not found. Installing...
    pip install nuitka ordered-set zstandard
    if %ERRORLEVEL% neq 0 (
        echo Failed to install Nuitka. Please install it manually:
        echo pip install nuitka ordered-set zstandard
        pause
        exit /b 1
    )
)

REM Clean up any existing build files
echo Cleaning up previous build files...
if exist main.py.build rd /s /q main.py.build
if exist main.py.dist rd /s /q main.py.dist
if exist optimized_build rd /s /q optimized_build
if exist nuitka-crash-report.xml del nuitka-crash-report.xml

REM Create output directory
mkdir optimized_build 2>nul

echo.
echo Building with Nuitka...
echo This may take several minutes...
echo.

REM Check if icon file exists
set "ICON_OPTION="
if exist icons\app_icon.ico (
    set "ICON_OPTION=--windows-icon-from-ico=icons/app_icon.ico"
) else (
    echo Warning: Icon file not found. Using default icon.
)

REM Build with Nuitka using optimized settings (all on one line to avoid PowerShell issues)
python -m nuitka --standalone --output-dir=optimized_build --output-filename=PDFHarvest --assume-yes-for-downloads --disable-ccache --follow-imports --enable-plugin=pyside6 --include-qt-plugins=sensible --include-package=cryptography --include-package=sqlite3 --include-package=PIL --include-package=pypdf_table_extraction --include-package=fitz --include-package=PySide6 --include-package=pandas --include-package=jmespath --include-package=requests --include-package=activation_dialog --include-package=license_manager --include-package=db_protection --include-package=path_helper --include-package=argparse --include-package=concurrent.futures --include-package=multiprocessing --nofollow-import-to=numpy.distutils --nofollow-import-to=numpy.f2py --nofollow-import-to=numpy.testing --nofollow-import-to=matplotlib --nofollow-import-to=scipy --nofollow-import-to=pytest --nofollow-import-to=setuptools --nofollow-import-to=distutils --nofollow-import-to=_tkinter --nofollow-import-to=IPython --nofollow-import-to=numba --nofollow-import-to=pytz --nofollow-import-to=docutils --nofollow-import-to=sphinx --nofollow-import-to=tkinter --nofollow-import-to=PyQt5 --nofollow-import-to=PyQt6 --nofollow-import-to=wx --nofollow-import-to=pandas.tests --nofollow-import-to=pandas.plotting --nofollow-import-to=pandas._testing --nofollow-import-to=pandas.io.formats.style --nofollow-import-to=pandas.io.excel._xlsxwriter --nofollow-import-to=pandas.io.excel._openpyxl --nofollow-import-to=pandas.io.excel._odswriter --nofollow-import-to=pandas.io.excel._pyxlsb --nofollow-import-to=pandas.io.clipboard --nofollow-import-to=pandas.util._decorators --nofollow-import-to=pandas.util._depr_module --nofollow-import-to=pandas.util._doctools --nofollow-import-to=pandas.util._print_versions --nofollow-import-to=pandas.util._test_decorators --nofollow-import-to=pandas.util._tester --nofollow-import-to=pandas.util._validators %ICON_OPTION% --windows-company-name="PDF Harvest" --windows-product-name="PDF Harvest" --windows-file-version=1.0.0 --windows-product-version=1.0.0 --windows-file-description="PDF Harvest - PDF Data Extraction Tool" --windows-disable-console main.py

if %ERRORLEVEL% neq 0 (
    echo.
    echo Build failed. See error messages above.
    pause
    exit /b 1
)

REM Find the build directory
set BUILD_DIR=
for /d %%d in (optimized_build\*.dist) do (
    set BUILD_DIR=%%d
    goto :found_build
)

:found_build
if not defined BUILD_DIR (
    echo.
    echo Error: Could not find build directory.
    pause
    exit /b 1
)

echo Found build directory: %BUILD_DIR%

REM Create logs directory
mkdir "%BUILD_DIR%\logs" 2>nul

REM Copy data files
echo Copying data files...
if exist *.db copy *.db "%BUILD_DIR%\"
if exist *.json copy *.json "%BUILD_DIR%\"
if exist *.enc copy *.enc "%BUILD_DIR%\"
if exist .db_salt copy .db_salt "%BUILD_DIR%\"

REM Create an empty user_management.db.enc file if it doesn't exist
if not exist user_management.db.enc (
    echo Creating empty user_management.db.enc file...
    copy NUL "%BUILD_DIR%\user_management.db.enc" >nul
)

if exist licenses xcopy /E /Y licenses "%BUILD_DIR%\licenses\" >nul

REM Create run with console batch file
echo Creating run with console batch file...
(
    echo @echo off
    echo REM Run PDFHarvest with console window
    echo start cmd /k PDFHarvest.exe
) > "%BUILD_DIR%\run_with_console.bat"

REM Create CLI wrapper batch file
echo Creating CLI wrapper batch file...
(
    echo @echo off
    echo REM PDFHarvest CLI wrapper
    echo REM Usage: pdf_extractor_cli [arguments]
    echo.
    echo PDFHarvest.exe --cli %%*
) > "%BUILD_DIR%\pdf_extractor_cli.bat"

REM Remove unnecessary files to reduce size
echo Removing unnecessary files to reduce size...
for /r "%BUILD_DIR%" %%f in (*.pyc) do del "%%f"
for /r "%BUILD_DIR%" %%f in (*_test.pyd) do del "%%f"
for /r "%BUILD_DIR%" %%f in (*test_*.pyd) do del "%%f"
for /r "%BUILD_DIR%" %%f in (*\tests\*.pyd) do del "%%f"

REM Remove unnecessary pandas modules
if exist "%BUILD_DIR%\pandas\plotting" rd /s /q "%BUILD_DIR%\pandas\plotting"
if exist "%BUILD_DIR%\pandas\tests" rd /s /q "%BUILD_DIR%\pandas\tests"
if exist "%BUILD_DIR%\pandas\_testing" rd /s /q "%BUILD_DIR%\pandas\_testing"

REM Remove unnecessary numpy modules
if exist "%BUILD_DIR%\numpy\distutils" rd /s /q "%BUILD_DIR%\numpy\distutils"
if exist "%BUILD_DIR%\numpy\f2py" rd /s /q "%BUILD_DIR%\numpy\f2py"
if exist "%BUILD_DIR%\numpy\testing" rd /s /q "%BUILD_DIR%\numpy\testing"
if exist "%BUILD_DIR%\numpy\tests" rd /s /q "%BUILD_DIR%\numpy\tests"

echo.
echo Analyzing distribution size...
dir /s "%BUILD_DIR%" | findstr "File(s)"

echo.
echo Build and setup completed successfully!
echo You can run the application by double-clicking on:
echo %BUILD_DIR%\PDFHarvest.exe
echo.
echo For console output, use:
echo %BUILD_DIR%\run_with_console.bat
echo.
echo For CLI functionality, use:
echo %BUILD_DIR%\pdf_extractor_cli.bat
echo.
pause
