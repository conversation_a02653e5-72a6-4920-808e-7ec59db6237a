@echo off
setlocal enabledelayedexpansion

echo ===================================================
echo PDF Harvest License Generator
echo ===================================================
echo.

REM Check if Python is available
where python >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo Error: Python not found. Please install Python 3.8 or higher.
    pause
    exit /b 1
)

REM Check if license_generator.py exists
if not exist "tools\license_generator.py" (
    echo Error: license_generator.py not found in the tools directory.
    echo Please make sure you're running this script from the main application directory.
    pause
    exit /b 1
)

:menu
cls
echo ===================================================
echo PDF Harvest License Generator
echo ===================================================
echo.
echo Please select an option:
echo.
echo 1. Generate Demo License
echo 2. Generate Basic License
echo 3. Generate Professional License
echo 4. Generate Custom License
echo 5. List Available License Editions
echo 6. Decode Existing License Key
echo 7. Exit
echo.
set /p choice="Enter your choice (1-7): "

if "%choice%"=="1" goto demo_license
if "%choice%"=="2" goto basic_license
if "%choice%"=="3" goto professional_license
if "%choice%"=="4" goto custom_license
if "%choice%"=="5" goto list_editions
if "%choice%"=="6" goto decode_license
if "%choice%"=="7" goto end

echo Invalid choice. Please try again.
timeout /t 2 >nul
goto menu

:demo_license
cls
echo ===================================================
echo Generate Demo License
echo ===================================================
echo.
echo Please enter the parameters for your demo license:
echo.
set /p days="Expiry days (default: 30): "
set /p file_limit="File limit (default: 10): "

if "!days!"=="" set days=30
if "!file_limit!"=="" set file_limit=10

echo.
echo Generating demo license with:
echo - Expiry days: !days!
echo - File limit: !file_limit!
echo.

python tools\license_generator.py demo --days !days! --file-limit !file_limit!

echo.
set /p save_file="Save license key to file? (Y/N): "
if /i "!save_file!"=="Y" (
    set /p filename="Enter filename (default: demo_license.txt): "
    if "!filename!"=="" set filename=demo_license.txt
    python tools\license_generator.py demo --days !days! --file-limit !file_limit! --output !filename!
    echo License key saved to !filename!
)

echo.
pause
goto menu

:basic_license
cls
echo ===================================================
echo Generate Basic License
echo ===================================================
echo.
echo Please enter the parameters for your basic license:
echo.
set /p days="Expiry days (default: 365): "
set /p file_limit="File limit (default: 50): "
set /p hardware_id="Hardware ID (leave empty for no hardware lock): "

if "!days!"=="" set days=365
if "!file_limit!"=="" set file_limit=50

echo.
echo Generating basic license with:
echo - Edition: Basic
echo - Expiry days: !days!
echo - File limit: !file_limit!
if not "!hardware_id!"=="" echo - Hardware ID: !hardware_id!
echo.

if "!hardware_id!"=="" (
    python tools\license_generator.py generate --edition basic --expiry-days !days! --file-limit !file_limit!
) else (
    python tools\license_generator.py generate --edition basic --expiry-days !days! --file-limit !file_limit! --hardware-id "!hardware_id!"
)

echo.
set /p save_file="Save license key to file? (Y/N): "
if /i "!save_file!"=="Y" (
    set /p filename="Enter filename (default: basic_license.txt): "
    if "!filename!"=="" set filename=basic_license.txt
    
    if "!hardware_id!"=="" (
        python tools\license_generator.py generate --edition basic --expiry-days !days! --file-limit !file_limit! --output !filename!
    ) else (
        python tools\license_generator.py generate --edition basic --expiry-days !days! --file-limit !file_limit! --hardware-id "!hardware_id!" --output !filename!
    )
    
    echo License key saved to !filename!
)

echo.
pause
goto menu

:professional_license
cls
echo ===================================================
echo Generate Professional License
echo ===================================================
echo.
echo Please enter the parameters for your professional license:
echo.
set /p days="Expiry days (default: 365): "
set /p file_limit="File limit (default: 500): "
set /p hardware_id="Hardware ID (leave empty for no hardware lock): "

if "!days!"=="" set days=365
if "!file_limit!"=="" set file_limit=500

echo.
echo Generating professional license with:
echo - Edition: Professional
echo - Expiry days: !days!
echo - File limit: !file_limit!
if not "!hardware_id!"=="" echo - Hardware ID: !hardware_id!
echo.

if "!hardware_id!"=="" (
    python tools\license_generator.py generate --edition professional --expiry-days !days! --file-limit !file_limit!
) else (
    python tools\license_generator.py generate --edition professional --expiry-days !days! --file-limit !file_limit! --hardware-id "!hardware_id!"
)

echo.
set /p save_file="Save license key to file? (Y/N): "
if /i "!save_file!"=="Y" (
    set /p filename="Enter filename (default: professional_license.txt): "
    if "!filename!"=="" set filename=professional_license.txt
    
    if "!hardware_id!"=="" (
        python tools\license_generator.py generate --edition professional --expiry-days !days! --file-limit !file_limit! --output !filename!
    ) else (
        python tools\license_generator.py generate --edition professional --expiry-days !days! --file-limit !file_limit! --hardware-id "!hardware_id!" --output !filename!
    )
    
    echo License key saved to !filename!
)

echo.
pause
goto menu

:custom_license
cls
echo ===================================================
echo Generate Custom License
echo ===================================================
echo.
echo Please enter the parameters for your custom license:
echo.
set /p edition="Edition (demo/basic/professional): "
set /p days="Expiry days: "
set /p file_limit="File limit: "
set /p hardware_id="Hardware ID (leave empty for no hardware lock): "
set /p features="Features (space-separated, e.g., basic_extraction export_data): "

if "!edition!"=="" (
    echo Edition is required.
    pause
    goto custom_license
)

echo.
echo Generating custom license with:
echo - Edition: !edition!
if not "!days!"=="" echo - Expiry days: !days!
if not "!file_limit!"=="" echo - File limit: !file_limit!
if not "!hardware_id!"=="" echo - Hardware ID: !hardware_id!
if not "!features!"=="" echo - Features: !features!
echo.

set cmd=python tools\license_generator.py generate --edition !edition!

if not "!days!"=="" set cmd=!cmd! --expiry-days !days!
if not "!file_limit!"=="" set cmd=!cmd! --file-limit !file_limit!
if not "!hardware_id!"=="" set cmd=!cmd! --hardware-id "!hardware_id!"
if not "!features!"=="" set cmd=!cmd! --features !features!

!cmd!

echo.
set /p save_file="Save license key to file? (Y/N): "
if /i "!save_file!"=="Y" (
    set /p filename="Enter filename (default: custom_license.txt): "
    if "!filename!"=="" set filename=custom_license.txt
    
    !cmd! --output !filename!
    
    echo License key saved to !filename!
)

echo.
pause
goto menu

:list_editions
cls
echo ===================================================
echo List Available License Editions
echo ===================================================
echo.

python tools\license_generator.py list-editions

echo.
pause
goto menu

:decode_license
cls
echo ===================================================
echo Decode Existing License Key
echo ===================================================
echo.
echo Please enter the license key to decode:
echo.
set /p license_key="License Key: "

if "!license_key!"=="" (
    echo License key is required.
    pause
    goto decode_license
)

echo.
echo Decoding license key:
echo !license_key!
echo.

python tools\license_generator.py decode "!license_key!"

echo.
pause
goto menu

:end
echo.
echo Thank you for using the PDF Harvest License Generator.
echo.
endlocal
exit /b 0
