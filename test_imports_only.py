#!/usr/bin/env python3
"""
Test just the imports to see where the hang is occurring
"""

import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_step_by_step():
    print("Testing imports step by step...")
    
    try:
        print("1. Testing basic imports...")
        import pandas as pd
        print("   ✅ pandas imported")
        
        print("2. Testing error_handler...")
        from error_handler import log_error, log_warning, log_info
        print("   ✅ error_handler imported")
        
        print("3. Testing extraction_params_utils...")
        from extraction_params_utils import validate_extraction_method
        print("   ✅ extraction_params_utils imported")
        
        print("4. Testing pdf_extraction_utils...")
        # This might be the problematic import
        from pdf_extraction_utils import extract_table, extract_tables
        print("   ✅ pdf_extraction_utils imported")
        
        print("5. Testing invoice2data...")
        try:
            import invoice2data
            print("   ✅ invoice2data imported")
        except ImportError:
            print("   ⚠ invoice2data not available (expected)")
        
        print("6. All imports successful!")
        return True
        
    except Exception as e:
        print(f"   ❌ Import failed at step: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_step_by_step()
    print(f"\nResult: {'SUCCESS' if success else 'FAILED'}")
    sys.exit(0 if success else 1)
