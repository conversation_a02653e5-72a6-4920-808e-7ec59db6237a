@echo off
echo ===================================================
echo PDF Harvest Hardware ID Utility
echo ===================================================
echo.
echo This utility will display the hardware ID of this computer
echo for use with hardware-locked licenses.
echo.

REM Check if Python is available
where python >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo Error: Python not found. Please install Python 3.8 or higher.
    pause
    exit /b 1
)

REM Check if license_manager.py exists
if not exist "license_manager.py" (
    echo Error: license_manager.py not found.
    echo Please make sure you're running this script from the main application directory.
    pause
    exit /b 1
)

echo Getting hardware ID...
echo.

python -c "from license_manager import get_license_manager; lm = get_license_manager(); print(f'Hardware ID: {lm.hardware_id}')"

echo.
echo You can use this hardware ID when generating a hardware-locked license.
echo.
pause
