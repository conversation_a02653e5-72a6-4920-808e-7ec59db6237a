#!/usr/bin/env python3
"""
Simple test script for multi-method PDF extraction system (no Qt required)

This script tests the core functionality without requiring Qt widgets.
"""

import os
import sys
import sqlite3

# Add the current directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_database_schema():
    """Test database schema for extraction method support"""
    print("Testing database schema for extraction method...")
    
    try:
        # Check if database exists
        if not os.path.exists("invoice_templates.db"):
            print("❌ Database file not found")
            return False
        
        # Connect to database
        conn = sqlite3.connect("invoice_templates.db")
        cursor = conn.cursor()
        
        # Check if extraction_method column exists
        cursor.execute("PRAGMA table_info(templates)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'extraction_method' in columns:
            print("✅ extraction_method column exists in templates table")
            
            # Test inserting a template with extraction method
            test_template = {
                'name': 'test_multi_method_simple',
                'description': 'Test template for multi-method extraction',
                'template_type': 'single',
                'extraction_method': 'pdftotext',
                'regions': '{}',
                'column_lines': '{}',
                'config': '{}'
            }
            
            try:
                cursor.execute("""
                    INSERT INTO templates (name, description, template_type, extraction_method, regions, column_lines, config)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (
                    test_template['name'],
                    test_template['description'],
                    test_template['template_type'],
                    test_template['extraction_method'],
                    test_template['regions'],
                    test_template['column_lines'],
                    test_template['config']
                ))
                
                # Get the inserted template
                cursor.execute("SELECT extraction_method FROM templates WHERE name = ?", (test_template['name'],))
                result = cursor.fetchone()
                
                if result and result[0] == 'pdftotext':
                    print("✅ Successfully inserted and retrieved template with extraction method")
                else:
                    print("❌ Failed to retrieve correct extraction method")
                    return False
                
                # Clean up test template
                cursor.execute("DELETE FROM templates WHERE name = ?", (test_template['name'],))
                conn.commit()
                
            except Exception as e:
                print(f"❌ Database operation failed: {e}")
                return False
        else:
            print("❌ extraction_method column not found in templates table")
            return False
        
        conn.close()
        print("✅ Database schema tests passed")
        return True
        
    except Exception as e:
        print(f"❌ Database schema tests failed: {e}")
        return False

def test_extraction_method_validation():
    """Test extraction method validation"""
    print("Testing extraction method validation...")
    
    try:
        from extraction_params_utils import validate_extraction_method, SUPPORTED_EXTRACTION_METHODS
        
        # Test valid methods
        for method in SUPPORTED_EXTRACTION_METHODS:
            assert validate_extraction_method(method), f"Valid method {method} failed validation"
            print(f"✅ {method} - valid")
        
        # Test invalid method
        assert not validate_extraction_method("invalid_method"), "Invalid method passed validation"
        print("✅ invalid_method - correctly rejected")
        
        print("✅ Extraction method validation tests passed")
        return True
        
    except Exception as e:
        print(f"❌ Extraction method validation tests failed: {e}")
        return False

def test_multi_method_extractor():
    """Test the MultiMethodExtractor class"""
    print("Testing MultiMethodExtractor class...")
    
    try:
        from multi_method_extraction import MultiMethodExtractor
        
        # Create extractor instance
        extractor = MultiMethodExtractor()
        print("✅ MultiMethodExtractor instance created")
        
        # Test method availability
        methods_available = {
            'pypdf_table_extraction': True,  # Always available
            'pdftotext': hasattr(extractor, '_extract_with_pdftotext'),
            'tesseract_ocr': hasattr(extractor, '_extract_with_tesseract')
        }
        
        for method, available in methods_available.items():
            status = "available" if available else "not available"
            print(f"✅ {method} - {status}")
        
        # Test cleanup
        extractor.cleanup()
        print("✅ Cleanup completed")
        
        print("✅ MultiMethodExtractor tests passed")
        return True
        
    except Exception as e:
        print(f"❌ MultiMethodExtractor tests failed: {e}")
        return False

def test_extraction_params_utils():
    """Test extraction parameters utilities"""
    print("Testing extraction parameters utilities...")
    
    try:
        from extraction_params_utils import (
            normalize_extraction_params,
            prepare_extraction_method_params,
            DEFAULT_SECTION_PARAMS
        )
        
        # Test parameter normalization
        raw_params = {
            'header': {'row_tol': 5},
            'items': {'row_tol': 15},
            'summary': {'row_tol': 10},
            'flavor': 'stream'
        }
        
        normalized = normalize_extraction_params(raw_params)
        assert isinstance(normalized, dict), "Normalized params should be a dict"
        print("✅ Parameter normalization works")
        
        # Test method-specific parameter preparation
        for method in ['pypdf_table_extraction', 'pdftotext', 'tesseract_ocr']:
            method_params = prepare_extraction_method_params(method, raw_params)
            assert isinstance(method_params, dict), f"Method params for {method} should be a dict"
            print(f"✅ {method} parameter preparation works")
        
        # Test default section parameters
        assert 'header' in DEFAULT_SECTION_PARAMS, "Default params should include header"
        assert 'items' in DEFAULT_SECTION_PARAMS, "Default params should include items"
        assert 'summary' in DEFAULT_SECTION_PARAMS, "Default params should include summary"
        print("✅ Default section parameters are properly defined")
        
        print("✅ Extraction parameters utilities tests passed")
        return True
        
    except Exception as e:
        print(f"❌ Extraction parameters utilities tests failed: {e}")
        return False

def test_imports():
    """Test that all required modules can be imported"""
    print("Testing module imports...")
    
    try:
        # Test core extraction modules
        from multi_method_extraction import extract_with_method, cleanup_extraction
        print("✅ multi_method_extraction imports successfully")
        
        from extraction_params_utils import validate_extraction_method
        print("✅ extraction_params_utils imports successfully")
        
        # Test that bulk processor can import the new modules
        import bulk_processor
        print("✅ bulk_processor imports successfully")
        
        print("✅ All module imports passed")
        return True
        
    except Exception as e:
        print(f"❌ Module import tests failed: {e}")
        return False

def main():
    """Run all tests"""
    print("=" * 60)
    print("Multi-Method PDF Extraction System Tests (Simple)")
    print("=" * 60)
    print()
    
    tests = [
        test_imports,
        test_extraction_method_validation,
        test_multi_method_extractor,
        test_extraction_params_utils,
        test_database_schema
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            print()  # Add spacing between tests
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
            print()
    
    print("=" * 60)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Multi-method extraction system is ready.")
        print()
        print("📋 Available extraction methods:")
        print("   • pypdf_table_extraction (default) - Standard table extraction using pypdf")
        print("   • pdftotext - Text extraction using invoice2data's pdftotext parser")
        print("   • tesseract_ocr - OCR extraction using invoice2data's tesseract parser")
        print()
        print("💡 Next steps:")
        print("   1. Templates can now specify their preferred extraction method")
        print("   2. Use the template manager to set extraction methods for templates")
        print("   3. The system will automatically use the specified method during extraction")
    else:
        print("⚠ Some tests failed. Please check the output above.")
    
    print("=" * 60)
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
