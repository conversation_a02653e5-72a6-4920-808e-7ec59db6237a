Metadata-Version: 2.1
Name: ghostscript
Version: 0.7
Summary: Interface to the Ghostscript C-API, both high- and low-level, based on ctypes
Home-page: https://gitlab.com/pdftools/python-ghostscript
Author: <PERSON><PERSON><PERSON>
Author-email: <EMAIL>
License: GNU General Public License v3 or later (GPLv3+)
Download-URL: http://pypi.python.org/pypi/ghostscript
Keywords: Ghostscript,PDF,Postscript
Platform: UNKNOWN
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: GNU General Public License v3 or later (GPLv3+)
Classifier: Natural Language :: English
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.6
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Description-Content-Type: text/x-rst
Requires-Dist: setuptools (>=38.6.0)

==========================
`python-ghostscript`
==========================

---------------------------------------------------------------------
Python-Interface to the Ghostscript C-API
---------------------------------------------------------------------

:Author:  Hartmut Goebel <<EMAIL>>
:Version: 0.7
:License: GNU General Public License v3 or later (GPLv3+)
:Homepage: https://gitlab.com/pdftools/python-ghostscript

`Ghostscript`__ is a well known interpreter for the PostScript
language and for PDF. This package implements a interface to the
`Ghostscript C-API`__ using `ctypes`__. Both a low-level and a pythonic,
high-level interface are provided.

__ http://www.ghostscript.com/
__ http://pages.cs.wisc.edu/~ghost/doc/cvs/API.htm
__ http://docs.python.org/library/ctypes.html


This package is currently tested only under GNU/Linux. Please report
whether it works in your environment, too. Thanks.


Example
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Here is an example for how to use the high-level interface of
`python-ghostscript`. This implements a very basic ps2pdf-tool::

  import sys
  import locale
  import ghostscript

  args = [
      "ps2pdf",	# actual value doesn't matter
      "-dNOPAUSE", "-dBATCH", "-dSAFER",
      "-sDEVICE=pdfwrite",
      "-sOutputFile=" + sys.argv[1],
      "-c", ".setpdfwrite",
      "-f",  sys.argv[2]
      ]

  # arguments have to be bytes, encode them
  encoding = locale.getpreferredencoding()
  args = [a.encode(encoding) for a in args]

  ghostscript.Ghostscript(*args)

Here an example for passing a string document to Ghostscript::

  doc = b"""%!
  /Helvetica findfont 20 scalefont setfont       
  50 50 moveto
  (Hello World) show
  showpage
  quit
  """

  import ghostscript

  args = b"""test.py
       -dNOPAUSE -dBATCH -dSAFER -sDEVICE=pdfwrite -sOutputFile=/tmp/out.pdf
       -c .setpdfwrite""".split()

  with ghostscript.Ghostscript(*args) as gs:
      gs.run_string(doc)


More examples can be found in the `examples` subdirectory of the
distribution archive.


Requirements and Installation
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

:Please note: This package is meant for developers. Even if there are
  some usable examples included, installations instructions are meant
  for developers.

`python-ghostscript` requires

* `Python`__ 3.6 or higher (tested with Python 3.6–3.9)
* `setuptools`__ for installation (see below)
* `Ghostscript`__ Version 9.0.8 or higher

__ http://www.python.org/download/
__ http://pypi.python.org/pypi/setuptools
__ http://www.ghostscript.com/


Installing python-ghostscript
---------------------------------

Since this package is meant for developers, we assume you have
experience in installing Python packages.

`python-ghostscript` is listed on `PyPI (Python Package Index)`__, so
you can install it using `pip install ghostscript` as usual. Please
refer to the manuals of `pip` for further information.

__ http://pypi.python.org/pypi

Alternatively you my download and unpack the source package of
`python-ghostscript` from http://pypi.python.org/pypi/ghostscript and
run::

   python ./setup.py install



.. Emacs config:
 Local Variables:
 mode: rst
 ispell-local-dictionary: "american"
 End:


