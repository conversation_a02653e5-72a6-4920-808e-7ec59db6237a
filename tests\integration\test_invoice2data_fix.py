"""
Test the fix for invoice2data import in split_screen_invoice_processor.py.
"""
import os
import sys
import unittest
import tempfile
import json
from pathlib import Path
import importlib

class TestInvoice2DataFix(unittest.TestCase):
    """Test the fix for invoice2data import in split_screen_invoice_processor.py."""

    def test_import_fix(self):
        """Test the fix for invoice2data import."""
        # First, check if invoice2data is installed
        try:
            import pkg_resources
            installed_packages = {pkg.key: pkg.version for pkg in pkg_resources.working_set}

            if 'invoice2data' not in installed_packages:
                self.skipTest("invoice2data not installed")
                return

            # Get the invoice2data package location
            import subprocess
            result = subprocess.run(
                [sys.executable, "-m", "pip", "show", "invoice2data"],
                capture_output=True,
                text=True,
                check=True
            )

            # Extract the location from pip show output
            location = None
            for line in result.stdout.splitlines():
                if line.startswith("Location:"):
                    location = line.split(":", 1)[1].strip()
                    break

            if not location:
                self.skipTest("Could not find invoice2data package location")
                return

            # Save the original sys.path
            original_path = sys.path.copy()

            try:
                # Remove the location from sys.path if it's there
                if location in sys.path:
                    sys.path.remove(location)

                # Try to import invoice2data - this might fail or succeed depending on the environment
                import_succeeded = False
                try:
                    # Force reload to ensure we're not using a cached import
                    if 'invoice2data' in sys.modules:
                        del sys.modules['invoice2data']

                    import invoice2data
                    import_succeeded = True
                    print(f"Note: invoice2data imported successfully even though we removed {location} from sys.path")
                    print(f"This suggests that invoice2data is installed in multiple locations or in a location that's already in sys.path")
                    print(f"Current sys.path: {sys.path}")
                except ImportError:
                    # This is expected in most cases
                    pass

                # If the import didn't succeed, apply our fix
                if not import_succeeded:
                    sys.path.append(location)

                    # Try to import invoice2data again - this should succeed
                    try:
                        # Force reload to ensure we're not using a cached import
                        if 'invoice2data' in sys.modules:
                            del sys.modules['invoice2data']

                        import invoice2data
                        from invoice2data import extract_data
                        from invoice2data.extract.loader import read_templates

                        # If we get here, the import was successful
                        self.assertTrue(True, "invoice2data imported successfully after adding location to sys.path")
                    except ImportError as e:
                        self.fail(f"Failed to import invoice2data after adding location to sys.path: {str(e)}")
                else:
                    # The import succeeded even without adding the location to sys.path
                    # This is fine, but we should still test that our fix doesn't break anything
                    sys.path.append(location)

                    try:
                        # Force reload to ensure we're not using a cached import
                        if 'invoice2data' in sys.modules:
                            del sys.modules['invoice2data']

                        import invoice2data
                        from invoice2data import extract_data
                        from invoice2data.extract.loader import read_templates

                        # If we get here, the import was still successful after adding the location
                        self.assertTrue(True, "invoice2data imported successfully with or without adding location to sys.path")
                    except ImportError as e:
                        self.fail(f"Failed to import invoice2data after adding location to sys.path (even though it worked before): {str(e)}")
            finally:
                # Restore the original sys.path
                sys.path = original_path

        except Exception as e:
            self.fail(f"Error testing invoice2data import fix: {str(e)}")

if __name__ == '__main__':
    unittest.main()
