import unittest
import pandas as pd
import sys
import os
import copy

# Add the parent directory to the path so we can import the modules
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

class TestMultipageRegionLabels(unittest.TestCase):
    """Test case for handling regions with the same name in different pages."""

    def test_duplicate_region_names_across_pages(self):
        """Test that regions with the same name in different pages are handled correctly."""
        # Create test data
        # Page 1 header data
        header_df1 = pd.DataFrame({
            'region_label': ['H1', 'H1'],
            'col1': ['value1', 'value2'],
            'col2': ['value3', 'value4'],
            '_page_number': [1, 1]
        })

        # Page 2 header data - same region label as page 1
        header_df2 = pd.DataFrame({
            'region_label': ['H1', 'H1'],
            'col1': ['value5', 'value6'],
            'col2': ['value7', 'value8'],
            '_page_number': [2, 2]
        })

        # Combine the DataFrames
        combined_df = pd.concat([header_df1, header_df2], ignore_index=True)

        # Create a copy to test our function
        test_df = combined_df.copy()

        # Define a simplified version of the function we're testing
        def update_region_labels_with_page_numbers(df):
            """Update region labels in a DataFrame to include page numbers"""
            if df is None or not isinstance(df, pd.DataFrame) or df.empty:
                return

            if 'region_label' not in df.columns:
                return

            # Create a mapping of old labels to new labels with page numbers
            label_mapping = {}
            for idx, row in df.iterrows():
                old_label = row['region_label']
                page_num = row['_page_number'] if '_page_number' in df.columns else 1

                # Check if the label already has a page number suffix
                if '_P' in old_label:
                    # Keep the existing label
                    new_label = old_label
                else:
                    # Add page number suffix
                    new_label = f"{old_label}_P{page_num}"

                label_mapping[idx] = new_label

            # Update the region labels
            for idx, new_label in label_mapping.items():
                df.at[idx, 'region_label'] = new_label

        # Apply our function to the test DataFrame
        update_region_labels_with_page_numbers(test_df)

        # Check that the region labels have been made unique with page numbers
        region_labels = test_df['region_label'].tolist()

        # Print the region labels for debugging
        print(f"Region labels in result: {region_labels}")

        # Check that we have the expected number of rows
        self.assertEqual(len(region_labels), 4)  # 2 rows from each of 2 pages

        # Check that the region labels include page numbers for both pages
        page1_labels = [label for label in region_labels if '_P1' in label]
        page2_labels = [label for label in region_labels if '_P2' in label]

        # We should have labels with page numbers for both pages
        self.assertEqual(len(page1_labels), 2, f"Expected 2 labels with _P1, got {len(page1_labels)}")
        self.assertEqual(len(page2_labels), 2, f"Expected 2 labels with _P2, got {len(page2_labels)}")

        # Check that we don't have any 'H1' without page suffix
        exact_h1_count = sum(1 for label in region_labels if label == 'H1')
        self.assertEqual(exact_h1_count, 0, f"Found {exact_h1_count} 'H1' labels without page numbers")

if __name__ == '__main__':
    unittest.main()
