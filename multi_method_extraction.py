"""
Multi-Method PDF Extraction Module

This module provides support for multiple PDF extraction methods including:
- pypdf_table_extraction (default)
- pdftotext (from invoice2data)
- tesseract OCR (from invoice2data)
"""

import os
import tempfile
import subprocess
import pandas as pd
from typing import Dict, List, Any, Optional, Union
from error_handler import log_error, log_warning, log_info
from extraction_params_utils import (
    prepare_extraction_method_params, 
    validate_extraction_method,
    SUPPORTED_EXTRACTION_METHODS
)

# Import existing extraction functions
from pdf_extraction_utils import extract_table, extract_tables

# Check for invoice2data availability
try:
    import invoice2data
    from invoice2data import extract_data
    from invoice2data.extract.loader import read_templates
    INVOICE2DATA_AVAILABLE = True
except ImportError:
    INVOICE2DATA_AVAILABLE = False
    log_warning("invoice2data not available. pdftotext and tesseract_ocr methods will not work.")

# Check for pytesseract availability
try:
    import pytesseract
    from PIL import Image
    import pdf2image
    TESSERACT_AVAILABLE = True
except ImportError:
    TESSERACT_AVAILABLE = False
    log_warning("pytesseract or PIL not available. tesseract_ocr method may not work properly.")


class MultiMethodExtractor:
    """Handles extraction using multiple methods"""
    
    def __init__(self):
        self.temp_dir = None
        
    def extract_with_method(self, 
                          pdf_path: str,
                          extraction_method: str,
                          page_number: int = 1,
                          table_areas: Optional[List[List[float]]] = None,
                          columns_list: Optional[List[List[float]]] = None,
                          section_type: str = "items",
                          extraction_params: Optional[Dict[str, Any]] = None,
                          use_cache: bool = True) -> Optional[Union[pd.DataFrame, List[pd.DataFrame]]]:
        """
        Extract data using the specified method
        
        Args:
            pdf_path: Path to PDF file
            extraction_method: Method to use for extraction
            page_number: Page number (1-based)
            table_areas: List of table area coordinates
            columns_list: List of column coordinates
            section_type: Type of section being extracted
            extraction_params: Extraction parameters
            use_cache: Whether to use caching
            
        Returns:
            Extracted data as DataFrame(s) or None if extraction failed
        """
        if not validate_extraction_method(extraction_method):
            log_error(f"Invalid extraction method: {extraction_method}")
            return None
            
        # Prepare method-specific parameters
        method_params = prepare_extraction_method_params(extraction_method, extraction_params or {})
        
        try:
            if extraction_method == "pypdf_table_extraction":
                return self._extract_with_pypdf(
                    pdf_path, page_number, table_areas, columns_list, 
                    section_type, extraction_params, use_cache
                )
            elif extraction_method == "pdftotext":
                return self._extract_with_pdftotext(
                    pdf_path, page_number, table_areas, method_params
                )
            elif extraction_method == "tesseract_ocr":
                return self._extract_with_tesseract(
                    pdf_path, page_number, table_areas, method_params
                )
            else:
                log_error(f"Unsupported extraction method: {extraction_method}")
                return None
                
        except Exception as e:
            log_error(f"Error in {extraction_method} extraction: {str(e)}")
            return None
    
    def _extract_with_pypdf(self, 
                           pdf_path: str,
                           page_number: int,
                           table_areas: Optional[List[List[float]]],
                           columns_list: Optional[List[List[float]]],
                           section_type: str,
                           extraction_params: Dict[str, Any],
                           use_cache: bool) -> Optional[Union[pd.DataFrame, List[pd.DataFrame]]]:
        """Extract using pypdf_table_extraction method"""
        log_info(f"Extracting with pypdf_table_extraction method")
        
        if not table_areas:
            log_warning("No table areas provided for pypdf extraction")
            return None
            
        try:
            if len(table_areas) > 1:
                # Multiple tables
                return extract_tables(
                    pdf_path=pdf_path,
                    page_number=page_number,
                    table_areas=table_areas,
                    columns_list=columns_list,
                    section_type=section_type,
                    extraction_params=extraction_params,
                    use_cache=use_cache
                )
            else:
                # Single table
                return extract_table(
                    pdf_path=pdf_path,
                    page_number=page_number,
                    table_area=table_areas[0],
                    columns=columns_list[0] if columns_list else None,
                    section_type=section_type,
                    extraction_params=extraction_params,
                    use_cache=use_cache
                )
        except Exception as e:
            log_error(f"pypdf_table_extraction failed: {str(e)}")
            return None
    
    def _extract_with_pdftotext(self, 
                               pdf_path: str,
                               page_number: int,
                               table_areas: Optional[List[List[float]]],
                               method_params: Dict[str, Any]) -> Optional[pd.DataFrame]:
        """Extract using pdftotext method"""
        if not INVOICE2DATA_AVAILABLE:
            log_error("invoice2data not available for pdftotext extraction")
            return None
            
        log_info(f"Extracting with pdftotext method")
        
        try:
            # Create temporary directory if needed
            if not self.temp_dir:
                self.temp_dir = tempfile.mkdtemp()
            
            # Use pdftotext to extract text
            temp_text_file = os.path.join(self.temp_dir, f"page_{page_number}.txt")
            
            # Build pdftotext command
            cmd = ["pdftotext"]
            
            # Add parameters
            if method_params.get("layout", True):
                cmd.append("-layout")
            if method_params.get("raw", False):
                cmd.append("-raw")
            if method_params.get("html", False):
                cmd.append("-html")
            if method_params.get("xml", False):
                cmd.append("-xml")
            if method_params.get("bbox", False):
                cmd.append("-bbox")
                
            # Add page specification
            cmd.extend(["-f", str(page_number), "-l", str(page_number)])
            
            # Add input and output files
            cmd.extend([pdf_path, temp_text_file])
            
            # Run pdftotext
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            
            # Read extracted text
            if os.path.exists(temp_text_file):
                with open(temp_text_file, 'r', encoding='utf-8') as f:
                    text_content = f.read()
                
                # Convert text to DataFrame
                lines = text_content.strip().split('\n')
                if lines:
                    # Simple text-to-table conversion
                    data = []
                    for line in lines:
                        if line.strip():
                            # Split by multiple spaces or tabs
                            parts = [part.strip() for part in line.split() if part.strip()]
                            if parts:
                                data.append(parts)
                    
                    if data:
                        # Create DataFrame with dynamic columns
                        max_cols = max(len(row) for row in data)
                        columns = [f"col_{i+1}" for i in range(max_cols)]
                        
                        # Pad rows to have same number of columns
                        padded_data = []
                        for row in data:
                            padded_row = row + [''] * (max_cols - len(row))
                            padded_data.append(padded_row)
                        
                        df = pd.DataFrame(padded_data, columns=columns)
                        log_info(f"pdftotext extracted {len(df)} rows")
                        return df
            
            log_warning("pdftotext extraction produced no data")
            return None
            
        except subprocess.CalledProcessError as e:
            log_error(f"pdftotext command failed: {e}")
            return None
        except Exception as e:
            log_error(f"pdftotext extraction failed: {str(e)}")
            return None
    
    def _extract_with_tesseract(self, 
                               pdf_path: str,
                               page_number: int,
                               table_areas: Optional[List[List[float]]],
                               method_params: Dict[str, Any]) -> Optional[pd.DataFrame]:
        """Extract using tesseract OCR method"""
        if not TESSERACT_AVAILABLE:
            log_error("pytesseract or PIL not available for tesseract OCR extraction")
            return None
            
        log_info(f"Extracting with tesseract OCR method")
        
        try:
            # Convert PDF page to image
            images = pdf2image.convert_from_path(
                pdf_path, 
                first_page=page_number, 
                last_page=page_number,
                dpi=300  # High DPI for better OCR
            )
            
            if not images:
                log_error("Failed to convert PDF page to image")
                return None
                
            image = images[0]
            
            # If table areas are specified, crop the image
            if table_areas:
                # Use the first table area for cropping
                x1, y1, x2, y2 = table_areas[0]
                
                # Convert PDF coordinates to image coordinates
                img_width, img_height = image.size
                # Assuming PDF coordinates are in points (72 DPI)
                scale_x = img_width / 612  # Standard PDF width in points
                scale_y = img_height / 792  # Standard PDF height in points
                
                # Convert and crop
                crop_x1 = int(x1 * scale_x)
                crop_y1 = int(y1 * scale_y)
                crop_x2 = int(x2 * scale_x)
                crop_y2 = int(y2 * scale_y)
                
                image = image.crop((crop_x1, crop_y1, crop_x2, crop_y2))
            
            # Perform OCR
            ocr_config = method_params.get("config", "--psm 6")
            lang = method_params.get("lang", "eng")
            
            text = pytesseract.image_to_string(
                image, 
                lang=lang, 
                config=ocr_config
            )
            
            # Convert OCR text to DataFrame
            lines = text.strip().split('\n')
            if lines:
                data = []
                for line in lines:
                    if line.strip():
                        # Split by multiple spaces or tabs
                        parts = [part.strip() for part in line.split() if part.strip()]
                        if parts:
                            data.append(parts)
                
                if data:
                    # Create DataFrame with dynamic columns
                    max_cols = max(len(row) for row in data)
                    columns = [f"col_{i+1}" for i in range(max_cols)]
                    
                    # Pad rows to have same number of columns
                    padded_data = []
                    for row in data:
                        padded_row = row + [''] * (max_cols - len(row))
                        padded_data.append(padded_row)
                    
                    df = pd.DataFrame(padded_data, columns=columns)
                    log_info(f"tesseract OCR extracted {len(df)} rows")
                    return df
            
            log_warning("tesseract OCR extraction produced no data")
            return None
            
        except Exception as e:
            log_error(f"tesseract OCR extraction failed: {str(e)}")
            return None
    
    def cleanup(self):
        """Clean up temporary files"""
        if self.temp_dir and os.path.exists(self.temp_dir):
            try:
                import shutil
                shutil.rmtree(self.temp_dir)
                self.temp_dir = None
                log_info("Cleaned up temporary extraction files")
            except Exception as e:
                log_warning(f"Failed to clean up temporary files: {str(e)}")


# Global extractor instance
_extractor = MultiMethodExtractor()

def extract_with_method(pdf_path: str,
                       extraction_method: str,
                       page_number: int = 1,
                       table_areas: Optional[List[List[float]]] = None,
                       columns_list: Optional[List[List[float]]] = None,
                       section_type: str = "items",
                       extraction_params: Optional[Dict[str, Any]] = None,
                       use_cache: bool = True) -> Optional[Union[pd.DataFrame, List[pd.DataFrame]]]:
    """
    Convenience function for multi-method extraction
    
    Args:
        pdf_path: Path to PDF file
        extraction_method: Method to use for extraction
        page_number: Page number (1-based)
        table_areas: List of table area coordinates
        columns_list: List of column coordinates
        section_type: Type of section being extracted
        extraction_params: Extraction parameters
        use_cache: Whether to use caching
        
    Returns:
        Extracted data as DataFrame(s) or None if extraction failed
    """
    return _extractor.extract_with_method(
        pdf_path, extraction_method, page_number, table_areas, 
        columns_list, section_type, extraction_params, use_cache
    )

def cleanup_extraction():
    """Clean up extraction resources"""
    _extractor.cleanup()
