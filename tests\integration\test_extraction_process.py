"""
Integration tests for the PDF extraction process.
"""
import os
import pytest
import pandas as pd
import tempfile
from pathlib import Path
from unittest.mock import patch, MagicMock

from pdf_extraction_utils import (
    extract_table,
    extract_tables,
    extract_invoice_tables,
    clean_dataframe
)

# Skip these tests if no sample PDF is available
pytestmark = pytest.mark.skipif(
    not Path(__file__).parent.parent.joinpath("data/sample.pdf").exists(),
    reason="Sample PDF not available"
)

class TestExtractionProcess:
    """Integration tests for the extraction process."""
    
    @pytest.fixture
    def mock_pypdf_table_extraction(self):
        """Mock the pypdf_table_extraction module."""
        with patch('pdf_extraction_utils.pypdf_table_extraction') as mock_extraction:
            # Create a mock table result
            mock_table = MagicMock()
            mock_table.df = pd.DataFrame({
                'Column1': ['Value1', 'Value2', 'Value3'],
                'Column2': [100, 200, 300],
                'Column3': ['A', 'B', 'C']
            })
            
            # Configure the mock to return the table
            mock_extraction.read_pdf.return_value = [mock_table]
            
            yield mock_extraction
    
    def test_extract_table_integration(self, sample_pdf_path, mock_pypdf_table_extraction):
        """Test the extract_table function with mocked extraction."""
        # Extract a table
        result = extract_table(
            pdf_path=sample_pdf_path,
            page_number=1,
            table_area=[100, 200, 300, 400],
            columns=[150, 250],
            section_type='items',
            use_cache=False
        )
        
        # Check the result
        assert not result.empty, "Result should not be empty"
        assert list(result.columns) == ['Column1', 'Column2', 'Column3'], "Columns should match"
        assert len(result) == 3, "Result should have 3 rows"
        
        # Check that pypdf_table_extraction was called correctly
        mock_pypdf_table_extraction.read_pdf.assert_called_once()
        args, kwargs = mock_pypdf_table_extraction.read_pdf.call_args
        
        # Check the arguments
        assert args[0] == sample_pdf_path, "PDF path should be passed correctly"
        assert kwargs['pages'] == '1', "Page number should be passed correctly"
        assert kwargs['table_areas'] == ['100,200,300,400'], "Table area should be passed correctly"
        assert kwargs['columns'] == ['150,250'], "Columns should be passed correctly"
    
    def test_extract_tables_integration(self, sample_pdf_path, mock_pypdf_table_extraction):
        """Test the extract_tables function with mocked extraction."""
        # Extract multiple tables
        results = extract_tables(
            pdf_path=sample_pdf_path,
            page_number=1,
            table_areas=[[100, 200, 300, 400], [500, 600, 700, 800]],
            columns_list=[[150, 250], [550, 650]],
            section_type='items',
            use_cache=False
        )
        
        # Check the results
        assert len(results) == 2, "Should return 2 tables"
        for result in results:
            assert not result.empty, "Result should not be empty"
            assert list(result.columns) == ['Column1', 'Column2', 'Column3'], "Columns should match"
            assert len(result) == 3, "Result should have 3 rows"
        
        # Check that pypdf_table_extraction was called correctly
        assert mock_pypdf_table_extraction.read_pdf.call_count == 2, "read_pdf should be called twice"

@pytest.mark.skipif(True, reason="Requires actual PDF file and database setup")
class TestEndToEndExtraction:
    """End-to-end tests for the extraction process.
    
    These tests require actual PDF files and database setup.
    They are skipped by default but can be enabled for manual testing.
    """
    
    def test_extract_invoice_tables_end_to_end(self, sample_pdf_path):
        """Test the extract_invoice_tables function with a real PDF."""
        # Define regions and column lines
        regions = {
            'header': [[100, 100, 500, 200]],
            'items': [[100, 250, 500, 400]],
            'summary': [[100, 450, 500, 550]]
        }
        
        column_lines = {
            'header': [[150, 100, 150, 200], [300, 100, 300, 200]],
            'items': [[150, 250, 150, 400], [300, 250, 300, 400]],
            'summary': [[150, 450, 150, 550], [300, 450, 300, 550]]
        }
        
        # Extract tables
        results = extract_invoice_tables(
            pdf_path=sample_pdf_path,
            regions=regions,
            column_lines=column_lines,
            page_number=1,
            use_cache=False
        )
        
        # Check the results
        assert 'header_tables' in results, "Should return header tables"
        assert 'items_tables' in results, "Should return items tables"
        assert 'summary_tables' in results, "Should return summary tables"
        
        # Check extraction status
        assert 'extraction_status' in results, "Should return extraction status"
        assert 'overall' in results['extraction_status'], "Should return overall status"
