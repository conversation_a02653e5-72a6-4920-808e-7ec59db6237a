"""
Unit tests for memory management functionality.
"""
import os
import pytest
import gc
import psutil
from unittest.mock import patch, MagicMock

from bulk_processor import BulkProcessor

class TestMemoryManagement:
    """Tests for memory management functionality."""

    @patch('bulk_processor.psutil.virtual_memory')
    def test_check_memory_usage(self, mock_virtual_memory):
        """Test checking memory usage."""
        # Mock the virtual_memory function
        mock_memory = MagicMock()
        mock_memory.percent = 85.0  # Above threshold
        mock_virtual_memory.return_value = mock_memory

        # Create a BulkProcessor
        processor = BulkProcessor()
        processor.memory_threshold = 80

        # Mock the memory_usage_label
        processor.memory_usage_label = MagicMock()

        # Mock the gc.collect function
        with patch('bulk_processor.gc.collect') as mock_gc_collect:
            # Check memory usage
            processor.check_memory_usage()

            # Check that gc.collect was called
            mock_gc_collect.assert_called_once()

            # Check that memory usage was updated
            processor.memory_usage_label.setText.assert_called_once_with("85.0%")

            # Check that current_memory_usage was updated
            assert processor.current_memory_usage == 85.0

    @patch('bulk_processor.psutil.virtual_memory')
    def test_check_memory_usage_below_threshold(self, mock_virtual_memory):
        """Test checking memory usage when below threshold."""
        # Mock the virtual_memory function
        mock_memory = MagicMock()
        mock_memory.percent = 75.0  # Below threshold
        mock_virtual_memory.return_value = mock_memory

        # Create a BulkProcessor
        processor = BulkProcessor()
        processor.memory_threshold = 80

        # Mock the memory_usage_label
        processor.memory_usage_label = MagicMock()

        # Mock the gc.collect function
        with patch('bulk_processor.gc.collect') as mock_gc_collect:
            # Check memory usage
            processor.check_memory_usage()

            # Check that gc.collect was not called
            mock_gc_collect.assert_not_called()

            # Check that memory usage was updated
            processor.memory_usage_label.setText.assert_called_once_with("75.0%")

            # Check that current_memory_usage was updated
            assert processor.current_memory_usage == 75.0

    @patch('bulk_processor.QTimer')
    @patch('bulk_processor.psutil.virtual_memory')
    def test_start_memory_monitoring(self, mock_virtual_memory, mock_qtimer):
        """Test starting memory monitoring."""
        # Mock the virtual_memory function
        mock_memory = MagicMock()
        mock_memory.percent = 50.0
        mock_virtual_memory.return_value = mock_memory

        # Create a BulkProcessor
        processor = BulkProcessor()

        # Mock the memory_usage_label and cache_size_label
        processor.memory_usage_label = MagicMock()
        processor.cache_size_label = MagicMock()

        # Start memory monitoring
        processor.start_memory_monitoring()

        # Check that QTimer was created and started
        assert processor.memory_monitor_timer is not None
        processor.memory_monitor_timer.timeout.connect.assert_called_once()
        processor.memory_monitor_timer.start.assert_called_once_with(5000)  # 5 seconds

        # Check that memory usage was updated
        processor.memory_usage_label.setText.assert_called_once_with("50.0%")

    @patch('bulk_processor.get_extraction_cache_stats')
    def test_update_memory_stats(self, mock_get_cache_stats):
        """Test updating memory statistics."""
        # Mock the get_extraction_cache_stats function
        mock_get_cache_stats.return_value = {
            'extraction_cache_size': 8,
            'multipage_cache_size': 2,
            'extraction_cache_keys': ['key1', 'key2'],
            'multipage_cache_keys': ['key3']
        }

        # Create a BulkProcessor
        processor = BulkProcessor()

        # Mock the memory_usage_label and cache_size_label
        processor.memory_usage_label = MagicMock()
        processor.cache_size_label = MagicMock()
        processor.current_memory_usage = 60.0

        # Update memory stats
        processor.update_memory_stats()

        # Check that labels were updated
        processor.memory_usage_label.setText.assert_called_once_with("60.0%")
        processor.cache_size_label.setText.assert_called_once_with("10 items")

    @patch('bulk_processor.clear_extraction_cache')
    @patch('bulk_processor.QMessageBox')
    def test_clear_memory_cache(self, mock_qmessagebox, mock_clear_cache):
        """Test clearing the memory cache."""
        # Mock the QMessageBox.information return value
        mock_qmessagebox.information.return_value = mock_qmessagebox.Ok

        # Create a BulkProcessor
        processor = BulkProcessor()

        # Mock the cache_size_label
        processor.cache_size_label = MagicMock()

        # Clear memory cache
        processor.clear_memory_cache()

        # Check that clear_extraction_cache was called
        mock_clear_cache.assert_called_once()

        # Check that cache size label was updated
        processor.cache_size_label.setText.assert_called_once_with("0 items")

        # Check that QMessageBox was shown
        mock_qmessagebox.information.assert_called_once()
