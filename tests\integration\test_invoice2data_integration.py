"""
Integration tests for invoice2data functionality.
"""
import os
import pytest
import json
import tempfile
from pathlib import Path
from unittest.mock import patch, MagicMock

# Skip these tests if invoice2data is not available
try:
    import invoice2data
    INVOICE2DATA_AVAILABLE = True
except ImportError:
    INVOICE2DATA_AVAILABLE = False

pytestmark = pytest.mark.skipif(
    not INVOICE2DATA_AVAILABLE,
    reason="invoice2data not available"
)

class TestInvoice2DataIntegration:
    """Integration tests for invoice2data functionality."""
    
    @pytest.fixture
    def mock_invoice2data(self):
        """Mock the invoice2data module."""
        with patch('invoice2data.extract_data') as mock_extract_data, \
             patch('invoice2data.extract.loader.read_templates') as mock_read_templates:
            
            # Configure the mock to return sample data
            mock_extract_data.return_value = {
                'amount': 100.0,
                'date': '2023-01-01',
                'invoice_number': 'INV-001',
                'issuer': 'Test Company',
                'currency': 'INR',
                'language': 'en',
                'custom_field': 'Custom Value'
            }
            
            # Configure the mock to return sample templates
            mock_template = MagicMock()
            mock_template.keywords = ['Test', 'Invoice']
            mock_read_templates.return_value = [mock_template]
            
            yield mock_extract_data, mock_read_templates
    
    def test_invoice2data_extraction(self, mock_invoice2data, tmp_path):
        """Test invoice2data extraction with mocked data."""
        mock_extract_data, mock_read_templates = mock_invoice2data
        
        # Create a sample text file
        sample_text = """
        Test Company
        Invoice Number: INV-001
        Date: 2023-01-01
        Amount: $100.00
        """
        
        text_file = tmp_path / "sample_invoice.txt"
        text_file.write_text(sample_text)
        
        # Import the function from bulk_processor
        from bulk_processor import BulkProcessor
        
        # Create a BulkProcessor instance
        processor = BulkProcessor()
        
        # Mock the necessary methods
        processor.get_selected_template_id = MagicMock(return_value=1)
        processor.load_template_from_database = MagicMock(return_value={
            'template_type': 'single',
            'invoice2data_template': {
                'issuer': 'Test Company',
                'keywords': ['Test', 'Invoice'],
                'fields': {
                    'invoice_number': 'Invoice Number: (\\w+-\\d+)',
                    'date': 'Date: (\\d{4}-\\d{2}-\\d{2})',
                    'amount': 'Amount: \\$(\\d+\\.\\d{2})'
                }
            }
        })
        
        # Call the method that would use invoice2data
        # This is a simplified version since we're mocking the actual extraction
        with patch('builtins.open', return_value=MagicMock()):
            # Simulate the process that would call invoice2data
            result = invoice2data.extract_data(
                str(text_file),
                templates=mock_read_templates.return_value
            )
        
        # Check the result
        assert result['amount'] == 100.0, "Amount should be extracted correctly"
        assert result['date'] == '2023-01-01', "Date should be extracted correctly"
        assert result['invoice_number'] == 'INV-001', "Invoice number should be extracted correctly"
        assert result['issuer'] == 'Test Company', "Issuer should be extracted correctly"
        
        # Check that invoice2data was called correctly
        mock_extract_data.assert_called_once()
        mock_read_templates.assert_called_once()

@pytest.mark.skipif(True, reason="Requires actual invoice2data templates and PDF files")
class TestEndToEndInvoice2Data:
    """End-to-end tests for invoice2data functionality.
    
    These tests require actual invoice2data templates and PDF files.
    They are skipped by default but can be enabled for manual testing.
    """
    
    def test_invoice2data_end_to_end(self, tmp_path):
        """Test invoice2data extraction with real templates and files."""
        # This test would use actual invoice2data templates and PDF files
        # It's skipped by default but can be enabled for manual testing
        
        # Create a sample invoice2data template
        template_content = {
            "issuer": "Test Company",
            "keywords": ["Test", "Invoice"],
            "fields": {
                "invoice_number": "Invoice Number: (\\w+-\\d+)",
                "date": "Date: (\\d{4}-\\d{2}-\\d{2})",
                "amount": "Amount: \\$(\\d+\\.\\d{2})"
            }
        }
        
        template_file = tmp_path / "test_template.yml"
        with open(template_file, 'w') as f:
            f.write(json.dumps(template_content))
        
        # Create a sample invoice text
        invoice_text = """
        Test Company
        Invoice Number: INV-001
        Date: 2023-01-01
        Amount: $100.00
        """
        
        invoice_file = tmp_path / "test_invoice.txt"
        with open(invoice_file, 'w') as f:
            f.write(invoice_text)
        
        # Import invoice2data
        from invoice2data import extract_data
        from invoice2data.extract.loader import read_templates
        
        # Read templates
        templates = read_templates([str(template_file)])
        
        # Extract data
        result = extract_data(str(invoice_file), templates=templates)
        
        # Check the result
        assert result is not None, "Result should not be None"
        assert 'amount' in result, "Amount should be extracted"
        assert 'date' in result, "Date should be extracted"
        assert 'invoice_number' in result, "Invoice number should be extracted"
