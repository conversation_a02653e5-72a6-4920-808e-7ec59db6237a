"""
Test configuration for pytest.
"""
import os
import sys
import pytest
from pathlib import Path

# Add the project root directory to the Python path
sys.path.insert(0, str(Path(__file__).parent.parent))

# Define fixtures that can be reused across tests
@pytest.fixture
def sample_pdf_path():
    """Return the path to a sample PDF file for testing."""
    # This is a placeholder - you'll need to create or provide a sample PDF
    sample_path = Path(__file__).parent / "data" / "sample.pdf"
    if not sample_path.exists():
        pytest.skip(f"Sample PDF not found at {sample_path}")
    return str(sample_path)

@pytest.fixture
def temp_output_dir(tmp_path):
    """Create a temporary directory for test outputs."""
    output_dir = tmp_path / "output"
    output_dir.mkdir()
    return output_dir
