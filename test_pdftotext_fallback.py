#!/usr/bin/env python3
"""
Test script for pdftotext fallback functionality

This script tests that the pdftotext method works even when invoice2data is not available
or when the invoice2data parser fails.
"""

import os
import sys
import tempfile

# Add the current directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_pdftotext_availability():
    """Test if pdftotext command is available"""
    print("Testing pdftotext command availability...")
    
    try:
        import subprocess
        result = subprocess.run(["pdftotext", "-v"], capture_output=True, text=True, check=True)
        print("✅ pdftotext command is available")
        print(f"   Version info: {result.stderr.strip()}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ pdftotext command failed: {e}")
        return False
    except FileNotFoundError:
        print("❌ pdftotext command not found")
        print("   Please install poppler-utils:")
        print("   - Windows: Download from https://poppler.freedesktop.org/")
        print("   - Linux: sudo apt-get install poppler-utils")
        print("   - macOS: brew install poppler")
        return False

def test_multi_method_extraction_fallback():
    """Test that multi-method extraction falls back properly"""
    print("Testing multi-method extraction fallback...")
    
    try:
        from multi_method_extraction import MultiMethodExtractor
        
        # Create extractor instance
        extractor = MultiMethodExtractor()
        
        # Test that pdftotext method is available even if invoice2data parser isn't
        print("✅ MultiMethodExtractor created successfully")
        
        # Check method availability
        has_pdftotext_method = hasattr(extractor, '_extract_with_pdftotext')
        has_fallback_method = hasattr(extractor, '_fallback_pdftotext_extraction')
        
        print(f"✅ pdftotext method available: {has_pdftotext_method}")
        print(f"✅ fallback method available: {has_fallback_method}")
        
        return True
        
    except Exception as e:
        print(f"❌ Multi-method extraction test failed: {e}")
        return False

def test_extraction_method_validation():
    """Test that pdftotext is still a valid extraction method"""
    print("Testing extraction method validation...")
    
    try:
        from extraction_params_utils import validate_extraction_method
        
        # Test that pdftotext is still valid
        is_valid = validate_extraction_method("pdftotext")
        print(f"✅ pdftotext validation: {is_valid}")
        
        if not is_valid:
            print("❌ pdftotext should be a valid extraction method")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Extraction method validation failed: {e}")
        return False

def create_test_pdf():
    """Create a simple test PDF for testing"""
    try:
        from reportlab.pdfgen import canvas
        from reportlab.lib.pagesizes import letter
        
        # Create a temporary PDF file
        temp_pdf = tempfile.NamedTemporaryFile(delete=False, suffix='.pdf')
        temp_pdf.close()
        
        # Create PDF content
        c = canvas.Canvas(temp_pdf.name, pagesize=letter)
        c.drawString(100, 750, "Test Invoice")
        c.drawString(100, 700, "Item 1    $10.00")
        c.drawString(100, 680, "Item 2    $20.00")
        c.drawString(100, 660, "Total     $30.00")
        c.save()
        
        print(f"✅ Created test PDF: {temp_pdf.name}")
        return temp_pdf.name
        
    except ImportError:
        print("⚠ reportlab not available, skipping PDF creation test")
        return None
    except Exception as e:
        print(f"❌ Failed to create test PDF: {e}")
        return None

def test_pdftotext_extraction(pdf_path):
    """Test actual pdftotext extraction"""
    if not pdf_path:
        print("⚠ No PDF available for extraction test")
        return True
    
    print(f"Testing pdftotext extraction on: {pdf_path}")
    
    try:
        from multi_method_extraction import extract_with_method
        
        # Test pdftotext extraction
        result = extract_with_method(
            pdf_path=pdf_path,
            extraction_method="pdftotext",
            page_number=1,
            table_areas=[[50, 600, 300, 750]],  # Rough area covering the test content
            section_type="items"
        )
        
        if result is not None:
            print(f"✅ pdftotext extraction successful")
            print(f"   Extracted {len(result)} rows with {len(result.columns)} columns")
            print(f"   Sample data: {result.head(2).to_dict()}")
            return True
        else:
            print("⚠ pdftotext extraction returned no data (this may be normal for some PDFs)")
            return True
            
    except Exception as e:
        print(f"❌ pdftotext extraction failed: {e}")
        return False
    finally:
        # Clean up test PDF
        try:
            if os.path.exists(pdf_path):
                os.unlink(pdf_path)
                print(f"✅ Cleaned up test PDF")
        except:
            pass

def main():
    """Run all tests"""
    print("=" * 60)
    print("PDFtotext Fallback Functionality Tests")
    print("=" * 60)
    print()
    
    tests = [
        test_pdftotext_availability,
        test_multi_method_extraction_fallback,
        test_extraction_method_validation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            print()  # Add spacing between tests
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
            print()
    
    # Test with actual PDF if possible
    test_pdf = create_test_pdf()
    if test_pdf:
        print("Testing actual PDF extraction...")
        if test_pdftotext_extraction(test_pdf):
            passed += 1
        total += 1
        print()
    
    print("=" * 60)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! PDFtotext fallback is working correctly.")
    else:
        print("⚠ Some tests failed. Check the output above for details.")
        
        if not test_pdftotext_availability():
            print("\n💡 To fix pdftotext issues:")
            print("   1. Install poppler-utils for your operating system")
            print("   2. Ensure pdftotext is in your system PATH")
            print("   3. Restart your terminal/IDE after installation")
    
    print("=" * 60)
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
