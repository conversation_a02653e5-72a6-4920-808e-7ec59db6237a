# Multi-Method PDF Extraction Implementation

## Overview

This document describes the implementation of a multi-method PDF extraction system that supports three different extraction methods:

1. **pypdf_table_extraction** (default) - Standard table extraction using pypdf
2. **pdftotext** - Text extraction using invoice2data's pdftotext parser
3. **tesseract_ocr** - OCR extraction using invoice2data's tesseract parser

## Implementation Details

### 1. Core Components

#### `extraction_params_utils.py`
- **Purpose**: Manages extraction method validation and parameter preparation
- **Key Functions**:
  - `validate_extraction_method()` - Validates extraction method names
  - `prepare_extraction_method_params()` - Prepares method-specific parameters
  - `normalize_extraction_params()` - Normalizes extraction parameters
- **Supported Methods**: `SUPPORTED_EXTRACTION_METHODS = ['pypdf_table_extraction', 'pdftotext', 'tesseract_ocr']`

#### `multi_method_extraction.py`
- **Purpose**: Provides unified interface for multiple extraction methods
- **Key Classes**:
  - `MultiMethodExtractor` - Main extraction class with method-specific implementations
- **Key Functions**:
  - `extract_with_method()` - Main extraction function
  - `cleanup_extraction()` - Resource cleanup
- **Integration**: Uses invoice2data's built-in parsers for pdftotext and tesseract

### 2. Database Schema Updates

#### Templates Table
Added `extraction_method` column to the templates table:
```sql
ALTER TABLE templates 
ADD COLUMN extraction_method TEXT DEFAULT 'pypdf_table_extraction'
```

#### Migration Scripts
- `migrate_extraction_method.py` - Migrates existing databases
- `create_database_with_extraction_method.py` - Creates new database with extraction method support

### 3. Updated Processing Components

#### `bulk_processor.py`
- Updated `extract_invoice_tables()` method to use multi-method extraction
- Added extraction method loading from template data
- Updated all `extract_table()` and `extract_tables()` calls to use `extract_with_method()`

#### `split_screen_invoice_processor.py`
- Added `current_extraction_method` attribute
- Updated all extraction calls to use multi-method extraction
- Added extraction method configuration in `_configure_extraction_parameters()`

#### `invoice_processing_utils.py`
- Updated extraction calls to use multi-method extraction
- Maintains backward compatibility with existing functionality

### 4. Method-Specific Implementation

#### pypdf_table_extraction (Default)
- Uses existing `extract_table()` and `extract_tables()` functions
- Maintains full compatibility with current functionality
- No additional dependencies required

#### pdftotext
- Uses invoice2data's `PdftotextParser`
- Fallback to direct pdftotext command if parser fails
- Supports layout preservation, raw text, HTML, XML, and bbox modes
- Converts text output to DataFrame format compatible with existing system

#### tesseract_ocr
- Uses invoice2data's `TesseractParser`
- Fallback to direct pytesseract if parser fails
- Supports multiple languages and OCR configurations
- Includes image cropping based on table areas
- Converts OCR output to DataFrame format compatible with existing system

### 5. Parameter Handling

#### Method-Specific Parameters

**pypdf_table_extraction**:
- `flavor`, `split_text`, `strip_text`, `edge_tol`, `parallel`
- Section-specific: `row_tol`, `column_tol`, `text_tolerance`

**pdftotext**:
- `layout`, `raw`, `html`, `xml`, `bbox`
- Layout preservation and text formatting options

**tesseract_ocr**:
- `lang`, `config`, `nice`, `timeout`
- OCR language and configuration settings

#### Parameter Normalization
- Automatic parameter mapping between methods
- Default values for missing parameters
- Section-specific parameter handling (header, items, summary)

### 6. Error Handling and Fallbacks

#### Graceful Degradation
- Falls back to pypdf_table_extraction if specified method fails
- Fallback implementations for pdftotext and tesseract_ocr
- Comprehensive error logging and reporting

#### Resource Management
- Automatic cleanup of temporary files
- Memory management for large documents
- Cache integration with existing system

### 7. Testing and Validation

#### Test Scripts
- `test_extraction_methods_simple.py` - Core functionality tests
- `test_multi_method_extraction.py` - Comprehensive test suite

#### Test Coverage
- ✅ Extraction method validation
- ✅ MultiMethodExtractor functionality
- ✅ Parameter preparation and normalization
- ✅ Database schema validation
- ✅ Module import verification

### 8. Integration Points

#### Template System
- Templates can specify preferred extraction method
- Method selection persisted in database
- UI dropdowns for method selection (ready for implementation)

#### Bulk Processing
- Automatic method selection based on template
- Batch processing with different methods per template
- Progress reporting and error handling

#### Split-Screen Processor
- Real-time method switching
- Method-specific parameter adjustment
- Visual feedback for method selection

### 9. Configuration and Setup

#### Dependencies
- **Required**: pypdf (always available)
- **Optional**: invoice2data (for pdftotext and tesseract_ocr)
- **Optional**: pytesseract, PIL, pdf2image (for tesseract fallback)

#### Installation
1. Install invoice2data: `pip install invoice2data`
2. Install tesseract dependencies: `pip install pytesseract pillow pdf2image`
3. Run database migration: `python migrate_extraction_method.py`

### 10. Usage Examples

#### Basic Usage
```python
from multi_method_extraction import extract_with_method

# Extract using pdftotext
df = extract_with_method(
    pdf_path="invoice.pdf",
    extraction_method="pdftotext",
    page_number=1,
    table_areas=[[100, 200, 500, 400]],
    section_type="items"
)
```

#### Template-Based Usage
```python
# Template automatically specifies extraction method
template_data = {
    'extraction_method': 'tesseract_ocr',
    'regions': {...},
    'column_lines': {...}
}

# Method is automatically selected from template
results = extract_invoice_tables(pdf_path, template_data)
```

### 11. Performance Considerations

#### Method Performance
- **pypdf_table_extraction**: Fastest, best for structured PDFs
- **pdftotext**: Medium speed, good for text-heavy documents
- **tesseract_ocr**: Slowest, best for scanned/image-based PDFs

#### Optimization
- Caching support for all methods
- Parallel processing capability
- Memory-efficient image handling for OCR

### 12. Future Enhancements

#### Planned Features
- UI dropdowns for method selection in template manager
- Method-specific parameter tuning interfaces
- Performance benchmarking and method recommendation
- Automatic method selection based on PDF characteristics

#### Extensibility
- Plugin architecture for additional extraction methods
- Custom parameter validation
- Method-specific post-processing pipelines

## Conclusion

The multi-method PDF extraction system provides a flexible, extensible foundation for handling diverse PDF formats and extraction requirements. The implementation maintains full backward compatibility while adding powerful new capabilities for text extraction and OCR processing.

All core functionality has been implemented and tested, with the system ready for production use. The modular design allows for easy addition of new extraction methods and customization of existing ones.
