#!/usr/bin/env python
"""
Test runner script for the PDF Extractor project.
"""
import os
import sys
import pytest
from pathlib import Path

def main():
    """Run the tests."""
    # Add the project root directory to the Python path
    project_root = Path(__file__).parent.parent
    sys.path.insert(0, str(project_root))
    
    # Define test arguments
    args = [
        # Show verbose output
        '-v',
        # Show test progress
        '--verbose',
        # Generate HTML report
        '--html=test_report.html',
        # Show local variables in tracebacks
        '--showlocals',
        # Run tests in the tests directory
        str(Path(__file__).parent)
    ]
    
    # Run the tests
    return pytest.main(args)

if __name__ == '__main__':
    sys.exit(main())
