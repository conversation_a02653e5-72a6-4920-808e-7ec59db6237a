"""
Unit tests for pdf_extraction_utils.py
"""
import os
import pytest
import pandas as pd
import numpy as np
from unittest.mock import patch, MagicMock

from pdf_extraction_utils import (
    clean_dataframe,
    get_extraction_cache_stats,
    clear_extraction_cache,
    _get_cache_key,
    convert_display_to_pdf_coords,
    convert_pdf_to_display_coords,
    get_scale_factors
)

class TestCacheManagement:
    """Tests for the cache management functions."""

    def test_cache_key_generation(self):
        """Test that cache keys are generated consistently."""
        # Test with list inputs
        key1 = _get_cache_key(
            pdf_path="test.pdf",
            page_number=1,
            table_area=[100, 200, 300, 400],
            columns=[50, 150, 250],
            section_type="items"
        )

        # Test with the same inputs to ensure consistency
        key2 = _get_cache_key(
            pdf_path="test.pdf",
            page_number=1,
            table_area=[100, 200, 300, 400],
            columns=[50, 150, 250],
            section_type="items"
        )

        assert key1 == key2, "Cache keys should be consistent for the same inputs"

        # Test with string inputs
        key3 = _get_cache_key(
            pdf_path="test.pdf",
            page_number=1,
            table_area="100,200,300,400",
            columns="50,150,250",
            section_type="items"
        )

        # Keys should be different for different input formats
        assert key1 != key3, "Cache keys should differ for different input formats"

    def test_clear_extraction_cache(self):
        """Test clearing the extraction cache."""
        # Mock the global cache
        with patch('pdf_extraction_utils._EXTRACTION_CACHE', {'key1': 'value1', 'key2': 'value2'}):
            # Clear the cache
            clear_extraction_cache()

            # Check that the cache is empty
            stats = get_extraction_cache_stats()
            total_size = stats.get('extraction_cache_size', 0) + stats.get('multipage_cache_size', 0)
            assert total_size == 0, "Cache should be empty after clearing"

class TestDataFrameOperations:
    """Tests for DataFrame operations."""

    def test_clean_dataframe_empty(self):
        """Test cleaning an empty DataFrame."""
        df = pd.DataFrame()
        result = clean_dataframe(df)
        assert result.empty, "Result should be empty for empty input"

    def test_clean_dataframe_with_empty_rows_and_columns(self):
        """Test cleaning a DataFrame with empty rows and columns."""
        # Create a DataFrame with empty rows and columns
        df = pd.DataFrame({
            'A': [1, 2, np.nan, np.nan],
            'B': [3, 4, 5, np.nan],
            'C': [np.nan, np.nan, np.nan, np.nan]
        })

        result = clean_dataframe(df)

        # Check that empty rows and columns are removed
        assert 'C' not in result.columns, "Empty column should be removed"
        assert len(result) == 3, "Empty row should be removed"
        assert result.shape == (3, 2), "Resulting shape should be (3, 2)"

    def test_clean_dataframe_with_empty_strings(self):
        """Test cleaning a DataFrame with empty strings."""
        # Create a DataFrame with empty strings
        df = pd.DataFrame({
            'A': [1, 2, '', ''],
            'B': [3, 4, 5, ''],
            'C': ['', '', '', '']
        })

        result = clean_dataframe(df)

        # Check that empty strings are treated as NaN and removed
        assert 'C' not in result.columns, "Column with only empty strings should be removed"
        assert len(result) == 3, "Row with all empty strings should be removed"

class TestCoordinateConversion:
    """Tests for coordinate conversion functions."""

    def test_convert_display_to_pdf_coords(self):
        """Test converting display coordinates to PDF coordinates."""
        # Define test parameters
        display_coords = [100, 200, 300, 400]
        scale_x = 2.0
        scale_y = 2.0
        page_height = 1000

        # Convert coordinates
        pdf_coords = convert_display_to_pdf_coords(
            display_coords, scale_x, scale_y, page_height
        )

        # Check conversion
        assert pdf_coords[0] == display_coords[0] * scale_x, "X1 coordinate should be scaled"
        assert pdf_coords[2] == display_coords[2] * scale_x, "X2 coordinate should be scaled"

        # Y coordinates are flipped in PDF coordinates (origin at bottom-left)
        assert pdf_coords[1] == page_height - (display_coords[3] * scale_y), "Y1 should be flipped and scaled"
        assert pdf_coords[3] == page_height - (display_coords[1] * scale_y), "Y2 should be flipped and scaled"

    def test_convert_pdf_to_display_coords(self):
        """Test converting PDF coordinates to display coordinates."""
        # Define test parameters
        pdf_coords = [200, 600, 600, 200]
        scale_x = 2.0
        scale_y = 2.0
        page_height = 1000

        # Convert coordinates
        display_coords = convert_pdf_to_display_coords(
            pdf_coords, scale_x, scale_y, page_height
        )

        # Check conversion
        assert display_coords[0] == pdf_coords[0] / scale_x, "X1 coordinate should be scaled down"
        assert display_coords[2] == pdf_coords[2] / scale_x, "X2 coordinate should be scaled down"

        # Y coordinates are flipped in display coordinates (origin at top-left)
        assert display_coords[1] == (page_height - pdf_coords[3]) / scale_y, "Y1 should be flipped and scaled down"
        assert display_coords[3] == (page_height - pdf_coords[1]) / scale_y, "Y2 should be flipped and scaled down"

    def test_get_scale_factors(self):
        """Test getting scale factors for coordinate conversion."""
        # Mock a PDF document
        mock_pdf = MagicMock()
        mock_page = MagicMock()
        mock_page.rect.width = 595
        mock_page.rect.height = 842
        mock_pdf.__getitem__.return_value = mock_page

        # Mock the display dimensions
        display_width = 800
        display_height = 600

        with patch('fitz.open', return_value=mock_pdf):
            # Get scale factors
            scale_x, scale_y, page_height = get_scale_factors(
                "test.pdf", 1, display_width, display_height
            )

            # Check scale factors
            expected_scale_x = 595 / 800
            expected_scale_y = 842 / 600

            assert scale_x == pytest.approx(expected_scale_x), "X scale factor should be calculated correctly"
            assert scale_y == pytest.approx(expected_scale_y), "Y scale factor should be calculated correctly"
            assert page_height == 842, "Page height should be returned correctly"
